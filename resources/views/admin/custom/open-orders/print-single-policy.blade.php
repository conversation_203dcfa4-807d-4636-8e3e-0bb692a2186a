<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوليصة شحن - {{ $order->policy_no }}</title>
    <style>
        /* تصميم مُحسَّن للطابعة الحرارية 60mm مطابق للصورة المرجعية */
        @page {
            size: 60mm auto;
            margin: 1.5mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            font-size: 7px;
            font-weight: 500;
            line-height: 1.2;
            color: #000;
            background: #fff;
            width: 57mm;
            margin: 0 auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .policy-container {
            width: 100%;
            padding: 1.5mm;
            border: 1.5px solid #000;
            border-radius: 1.5mm;
            margin-bottom: 2mm;
        }

        /* تصميم الباركود مطابق للصورة المرجعية */
        .barcode-section {
            text-align: center;
            margin: 1.5mm 0;
            padding: 1.5mm;
            border: 1px solid #000;
            background: #fff;
        }

        .barcode-image {
            width: 50mm;
            height: 10mm;
            background: #fff;
            border: 1px solid #000;
            margin: 0.8mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .barcode-image img {
            max-width: 95%;
            max-height: 90%;
            object-fit: contain;
        }

        .barcode-number {
            font-size: 8px;
            font-weight: 900;
            margin-top: 0.8mm;
            text-align: center;
            color: #000;
            letter-spacing: 0.5px;
        }

        /* معلومات الشركة */
        .company-info {
            text-align: center;
            margin: 1.5mm 0;
            padding: 0.8mm;
        }

        .company-name {
            font-size: 9px;
            font-weight: 900;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.3px;
        }

        .phone-number {
            font-size: 7px;
            font-weight: bold;
            margin-bottom: 0.4mm;
            color: #000;
            letter-spacing: 0.2px;
        }

        .address-line {
            font-size: 6px;
            font-weight: bold;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.2px;
        }

        /* قسم السعر */
        .price-section {
            text-align: center;
            margin: 1.5mm 0;
            padding: 1.5mm;
            background: #fff;
            border: 2px solid #000;
        }

        .price-value {
            font-size: 12px;
            font-weight: 900;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.5px;
        }

        /* تحسينات للطباعة الحرارية */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-weight: 600 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
            }

            .policy-container {
                page-break-inside: avoid;
            }

            .no-print {
                display: none;
            }

            /* تحسين وضوح النصوص في الطباعة */
            .company-name {
                font-weight: 900 !important;
                color: #000 !important;
            }

            .phone-number {
                font-weight: bold !important;
                color: #000 !important;
            }

            .address-line {
                font-weight: bold !important;
                color: #000 !important;
            }

            .price-value {
                font-weight: 900 !important;
                color: #000 !important;
                font-size: 12px !important;
            }

            .barcode-number {
                font-weight: 900 !important;
                color: #000 !important;
            }

            .price-section {
                background: #fff !important;
                border: 2px solid #000 !important;
            }
        }

        /* أزرار التحكم */
        .control-buttons {
            text-align: center;
            margin: 3mm 0;
            padding: 1.5mm;
            background: #f0f0f0;
            border: 1px solid #ccc;
        }

        .btn {
            padding: 3px 10px;
            margin: 0 3px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
        }

        .btn-print {
            background: #007bff;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>

<body>
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">طباعة</button>
        <button class="btn btn-close" onclick="window.close()">إغلاق</button>
    </div>

    <div class="policy-container">
        <!-- Barcode Section - في الأعلى مطابق للصورة المرجعية -->
        <div class="barcode-section">
            <div class="barcode-image">
                @php
                    $generatorPNG = new Picqer\Barcode\BarcodeGeneratorPNG();
                @endphp
                <img src="data:image/png;base64,{{ base64_encode($generatorPNG->getBarcode($order->policy_no, $generatorPNG::TYPE_CODE_128)) }}"
                    alt="Barcode" style="max-width: 100%; height: auto;">
            </div>
            <div class="barcode-number">{{ $order->policy_no }}</div>
        </div>

        <!-- Company Information -->
        <div class="company-info">
            <div class="company-name">({{ $order->company->name }})</div>
            <div class="phone-number">الجوال: {{ $order->client->phone }}</div>
            <div class="address-line">{{ $order->client->address }} - {{ $order->area->name ?? 'غير محدد' }}</div>
        </div>

        <!-- Price Section -->
        <div class="price-section">
            <div class="price-value">EGP {{ $order->policy_value }}</div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>

</html>
