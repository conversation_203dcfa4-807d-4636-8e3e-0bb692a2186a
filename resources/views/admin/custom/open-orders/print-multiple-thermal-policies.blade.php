<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة بوالص حرارية متعددة</title>
    <style>
        /* تصميم مُحسَّن للطابعة الحرارية 60mm */
        @page {
            size: 60mm auto;
            margin: 0.8mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            width: 100%;
            background: #f5f5f5;
        }

        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            font-size: 7px;
            font-weight: 500;
            line-height: 1.2;
            color: #000;
            background: #fff;
            width: 57mm;
            margin: 0 auto;
            max-width: 57mm;
            min-width: 57mm;
            padding: 3mm;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .policy-container {
            width: 100%;
            padding: 1.5mm;
            border: 1.5px solid #000;
            border-radius: 1.5mm;
            margin-bottom: 3mm;
            page-break-inside: avoid;
        }

        /* فاصل بين البوالص */
        .policy-separator {
            height: 2mm;
            border-bottom: 1.5px dashed #000;
            margin: 2mm 0;
            page-break-after: avoid;
        }

        /* تصميم الباركود مطابق للصورة المرجعية */
        .barcode-section {
            text-align: center;
            margin: 1.5mm 0;
            padding: 1.5mm;
            border: 1px solid #000;
            background: #fff;
        }

        .barcode-image {
            width: 50mm;
            height: 10mm;
            background: #fff;
            border: 1px solid #000;
            margin: 0.8mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .barcode-image img {
            max-width: 95%;
            max-height: 90%;
            object-fit: contain;
        }

        .barcode-number {
            font-size: 8px;
            font-weight: 900;
            margin-top: 0.8mm;
            text-align: center;
            color: #000;
            letter-spacing: 0.5px;
        }

        /* معلومات الشركة */
        .company-info {
            text-align: center;
            margin: 1.5mm 0;
            padding: 0.8mm;
        }

        .company-name {
            font-size: 9px;
            font-weight: 900;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.3px;
        }

        .phone-number {
            font-size: 7px;
            font-weight: bold;
            margin-bottom: 0.4mm;
            color: #000;
            letter-spacing: 0.2px;
        }

        .address-line {
            font-size: 6px;
            font-weight: bold;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.2px;
        }

        /* قسم السعر */
        .price-section {
            text-align: center;
            margin: 1.5mm 0;
            padding: 1.5mm;
            background: #fff;
            border: 2px solid #000;
        }

        .price-value {
            font-size: 12px;
            font-weight: 900;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.5px;
        }

        /* تحسينات للطباعة الحرارية */
        @media print {
            @page {
                size: 60mm auto;
                margin: 1.5mm;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 57mm !important;
                font-size: 7px !important;
                font-weight: 600 !important;
                margin: 0 auto;
                padding: 0 !important;
                box-shadow: none !important;
                background: #fff !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
            }

            .policy-container {
                page-break-inside: avoid;
                width: 100% !important;
                padding: 1.5mm !important;
                border: 1.5px solid #000 !important;
                border-radius: 1.5mm !important;
                margin-bottom: 3mm !important;
            }

            .no-print {
                display: none;
            }

            .barcode-image {
                width: 50mm !important;
                height: 10mm !important;
            }

            .barcode-image img {
                max-width: 95% !important;
                max-height: 90% !important;
            }

            .barcode-number {
                font-size: 8px !important;
                font-weight: 900 !important;
                color: #000 !important;
            }

            .company-name {
                font-size: 9px !important;
                font-weight: 900 !important;
                color: #000 !important;
            }

            .phone-number {
                font-size: 7px !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            .address-line {
                font-size: 6px !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            .price-value {
                font-size: 12px !important;
                font-weight: 900 !important;
                color: #000 !important;
            }

            .price-section {
                background: #fff !important;
                border: 2px solid #000 !important;
            }
        }

        /* أزرار التحكم */
        .control-buttons {
            text-align: center;
            margin: 3mm 0;
            padding: 1.5mm;
            background: #f0f0f0;
            border: 1px solid #ccc;
            width: 100%;
            max-width: 57mm;
        }

        .btn {
            padding: 3px 10px;
            margin: 0 3px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
        }

        .btn-print {
            background: #28a745;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }



        /* عداد البوالص */
        .policy-counter {
            text-align: center;
            font-size: 9px;
            font-weight: bold;
            margin: 2mm 0;
            padding: 1.5mm;
            background: #e9ecef;
            border: 1px solid #000;
            width: 100%;
            max-width: 57mm;
        }
    </style>
</head>

<body>
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">طباعة حرارية</button>
        <button class="btn btn-close" onclick="window.close()">إغلاق</button>
    </div>

    <div class="policy-counter no-print">
        إجمالي البوالص: {{ count($orders) }} بوليصة
    </div>

    @php
        $generatorPNG = new Picqer\Barcode\BarcodeGeneratorPNG();
    @endphp

    @foreach ($orders as $index => $order)
        <div class="policy-container">
            <!-- Barcode Section - في الأعلى مطابق للصورة المرجعية -->
            <div class="barcode-section">
                <div class="barcode-image">
                    @php
                        // تحويل الأحرف إلى أرقام للباركود
                        $barcodeData = $order->policy_no;
                        if (preg_match('/[A-Za-z]/', $barcodeData)) {
                            // إذا كان يحتوي على أحرف، نحولها إلى أرقام
                            $barcodeData = preg_replace_callback(
                                '/[A-Za-z]/',
                                function ($matches) {
                                    return ord(strtoupper($matches[0])) - 64; // A=1, B=2, etc.
                                },
                                $barcodeData,
                            );
                        }
                    @endphp
                    <img src="data:image/png;base64,{{ base64_encode($generatorPNG->getBarcode($barcodeData, $generatorPNG::TYPE_CODE_128)) }}"
                        alt="Barcode" style="max-width: 100%; height: auto;">
                </div>
                <div class="barcode-number">{{ $order->policy_no }}</div>
            </div>

            <!-- Company Information -->
            <div class="company-info">
                <div class="company-name">({{ $order->company->name }})</div>
                <div class="phone-number">الجوال: {{ $order->client->phone }}</div>
                <div class="address-line">{{ $order->client->address }} - {{ $order->area->name ?? 'غير محدد' }}</div>
            </div>

            <!-- Price Section -->
            <div class="price-section">
                <div class="price-value">EGP {{ $order->policy_value }}</div>
            </div>
        </div>

        @if (!$loop->last)
            <div class="policy-separator"></div>
        @endif
    @endforeach

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>

</html>
