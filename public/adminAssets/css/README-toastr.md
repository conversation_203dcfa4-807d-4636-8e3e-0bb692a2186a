# إشعارات Toastr المخصصة للموقع

## نظرة عامة

تم تخصيص إشعارات Toastr لتظهر في الأعلى في المنتصف بدلاً من الأعلى يمين، مع تحسينات في التصميم والوظائف.

## الملفات المضافة

### 1. ملف CSS المخصص

**المسار:** `public/adminAssets/css/custom-toastr.css`

**الميزات:**

-   موقع الإشعارات في الأعلى في المنتصف
-   تصميم محسن مع ظلال وزوايا مدورة
-   ألوان مخصصة لكل نوع إشعار
-   دعم الشاشات المختلفة (متجاوب)
-   تحسينات للنصوص العربية
-   رسوم متحركة محسنة

### 2. ملف JavaScript المخصص

**المسار:** `public/adminAssets/js/custom-toastr.js`

**الوظائف المتاحة:**

```javascript
// الوظائف الأساسية
ToastrHelper.success(message, title);
ToastrHelper.error(message, title);
ToastrHelper.warning(message, title);
ToastrHelper.info(message, title);

// وظائف متقدمة
ToastrHelper.custom(type, message, title, icon);
ToastrHelper.timed(type, message, title, duration);
ToastrHelper.persistent(type, message, title);
ToastrHelper.clear();

// وظائف خاصة بالباركود
ToastrHelper.barcodeSuccess(policyNo, clientName, policyValue);
ToastrHelper.barcodeError(policyNo);

// وظائف خاصة بتعيين المندوبين
ToastrHelper.assignmentSuccess(
    policyNo,
    clientName,
    deliveryManName,
    policyValue
);
ToastrHelper.assignmentError(policyNo);

// وظائف التحذير من التعيين المسبق
ToastrHelper.alreadyAssignedSame(
    policyNo,
    clientName,
    deliveryManName,
    assignedDate
);
ToastrHelper.alreadyAssignedOther(
    policyNo,
    clientName,
    deliveryManName,
    assignedDate
);
```

## التطبيق على الموقع

### 1. ملف التخطيط الرئيسي

تم تحديث `resources/views/components/layouts/app.blade.php`:

-   إضافة ملف CSS المخصص
-   إضافة ملف JavaScript المخصص
-   تحديث معالج الأحداث ليستخدم الوظائف الجديدة

### 2. صفحة الباركود

تم تحديث `resources/views/livewire/custom/open-company-orders-list.blade.php`:

-   إزالة الاستايل المحلي
-   استخدام الوظائف الجديدة للإشعارات

## كيفية الاستخدام

### في PHP (Livewire)

```php
// إرسال إشعار نجاح
$this->emit('alert', ['type' => 'success', 'message' => 'تم الحفظ بنجاح']);

// إرسال إشعار خطأ
$this->emit('alert', ['type' => 'error', 'message' => 'حدث خطأ']);
```

### في JavaScript

```javascript
// إشعار نجاح
ToastrHelper.success("تم الحفظ بنجاح", "نجح!");

// إشعار خطأ
ToastrHelper.error("حدث خطأ", "خطأ!");

// إشعار مخصص مع أيقونة
ToastrHelper.custom("success", "رسالة النجاح", "العنوان", "zmdi zmdi-check");

// إشعار بمدة مخصصة (10 ثواني)
ToastrHelper.timed("info", "رسالة مهمة", "معلومات", 10000);

// إشعار دائم (لا يختفي تلقائياً)
ToastrHelper.persistent("warning", "تحذير مهم", "انتباه!");
```

## أنواع الإشعارات

### 1. النجاح (Success)

-   **اللون:** أخضر (#28a745)
-   **الاستخدام:** العمليات الناجحة
-   **المثال:** حفظ البيانات، استلام الطلبات

### 2. الخطأ (Error)

-   **اللون:** أحمر (#dc3545)
-   **الاستخدام:** الأخطاء والمشاكل
-   **المثال:** فشل الحفظ، عدم العثور على البيانات

### 3. التحذير (Warning)

-   **اللون:** أصفر (#ffc107)
-   **الاستخدام:** التحذيرات والتنبيهات
-   **المثال:** بيانات ناقصة، تحذيرات الأمان

### 4. المعلومات (Info)

-   **اللون:** أزرق (#17a2b8)
-   **الاستخدام:** المعلومات العامة
-   **المثال:** نصائح، معلومات إضافية

## الإعدادات الافتراضية

```javascript
{
    "closeButton": true,           // زر الإغلاق
    "newestOnTop": true,          // الأحدث في الأعلى
    "progressBar": true,          // شريط التقدم
    "positionClass": "toast-top-center", // الموقع
    "timeOut": "5000",            // مدة العرض (5 ثواني)
    "showDuration": "300",        // مدة الظهور
    "hideDuration": "1000",       // مدة الاختفاء
    "showMethod": "fadeIn",       // طريقة الظهور
    "hideMethod": "fadeOut"       // طريقة الاختفاء
}
```

## التوافق

-   ✅ جميع المتصفحات الحديثة
-   ✅ الشاشات المختلفة (متجاوب)
-   ✅ النصوص العربية
-   ✅ Livewire
-   ✅ jQuery

## استخدام تعيين المندوبين بالباركود

### في صفحة تعيين المندوبين

```javascript
// إشعار نجاح التعيين
ToastrHelper.assignmentSuccess("A123", "أحمد محمد", "محمد علي", "500");

// إشعار خطأ في التعيين
ToastrHelper.assignmentError("A123");
```

### الميزات الخاصة بتعيين المندوبين

-   ✅ **تعيين مباشر:** بدون تأكيد إضافي
-   ✅ **تحويل الباركود:** دعم الأحرف المحولة لأرقام
-   ✅ **التحقق من المندوب:** يجب اختيار المندوب أولاً
-   ✅ **تحديث تلقائي:** تحديث قائمة الطلبات بعد التعيين
-   ✅ **إشعارات مفصلة:** عرض تفاصيل الطلب والمندوب
-   ✅ **التحقق من التعيين المسبق:** تحذير عند محاولة تعيين طلب مُعيَّن مسبقاً

### حالات التحذير من التعيين المسبق

#### 1. مُعيَّن لنفس المندوب

```javascript
// عند محاولة تعيين طلب مُعيَّن بالفعل لنفس المندوب المختار
ToastrHelper.alreadyAssignedSame("A123", "أحمد محمد", "محمد علي", "2024-01-15");
```

-   **اللون:** أصفر (تحذير)
-   **الصوت:** نغمة تحذيرية متوسطة
-   **الرسالة:** تفاصيل الطلب والمندوب وتاريخ التعيين

#### 2. مُعيَّن لمندوب آخر

```javascript
// عند محاولة تعيين طلب مُعيَّن بالفعل لمندوب آخر
ToastrHelper.alreadyAssignedOther(
    "A123",
    "أحمد محمد",
    "علي أحمد",
    "2024-01-15"
);
```

-   **اللون:** أحمر (خطأ)
-   **الصوت:** نغمة تحذيرية
-   **الرسالة:** تفاصيل الطلب والمندوب المُعيَّن إليه وتاريخ التعيين

## الصيانة

لتحديث الإعدادات أو إضافة ميزات جديدة، قم بتعديل:

1. `custom-toastr.css` للتصميم
2. `custom-toastr.js` للوظائف
