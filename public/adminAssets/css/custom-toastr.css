/* 
 * تخصيص إشعارات Toastr للموقع بالكامل
 * Custom Toastr Notifications Styling
 */

/* تخصيص موقع الإشعارات لتظهر في الأعلى في المنتصف */
#toast-container {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    right: auto !important;
    width: auto !important;
    max-width: 500px !important;
    min-width: 300px !important;
    z-index: 999999 !important;
}

/* تخصيص إضافي للموقع */
.toast-top-center {
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    right: auto !important;
}

/* تحسين شكل الإشعارات */
.toast {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    margin-bottom: 10px !important;
    min-width: 300px !important;
    max-width: 500px !important;
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    font-family: 'Arial', 'Tahoma', sans-serif !important;
}

/* تحسين إشعارات النجاح */
.toast-success {
    background-color: #28a745 !important;
    border-left: 5px solid #1e7e34 !important;
}

/* تحسين إشعارات الخطأ */
.toast-error {
    background-color: #dc3545 !important;
    border-left: 5px solid #c82333 !important;
}

/* تحسين إشعارات التحذير */
.toast-warning {
    background-color: #ffc107 !important;
    border-left: 5px solid #e0a800 !important;
    color: #212529 !important;
}

/* تحسين إشعارات المعلومات */
.toast-info {
    background-color: #17a2b8 !important;
    border-left: 5px solid #138496 !important;
}

/* تحسين النص */
.toast-message {
    font-size: 14px !important;
    line-height: 1.4 !important;
    white-space: pre-line !important;
    padding: 8px 12px !important;
}

/* تحسين العنوان */
.toast-title {
    font-weight: bold !important;
    font-size: 16px !important;
    margin-bottom: 5px !important;
    padding: 8px 12px 0 12px !important;
}

/* تحسين شريط التقدم */
.toast-progress {
    height: 3px !important;
    border-radius: 0 0 8px 8px !important;
}

/* تحسين زر الإغلاق */
.toast-close-button {
    position: absolute !important;
    right: 8px !important;
    top: 8px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 18px !important;
    font-weight: bold !important;
    line-height: 1 !important;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1) !important;
    opacity: 0.8 !important;
    transition: opacity 0.3s ease !important;
}

.toast-close-button:hover {
    opacity: 1 !important;
    color: #fff !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    #toast-container {
        max-width: 90% !important;
        min-width: 280px !important;
        top: 10px !important;
    }
    
    .toast {
        min-width: 280px !important;
        max-width: 100% !important;
    }
    
    .toast-message {
        font-size: 13px !important;
    }
    
    .toast-title {
        font-size: 15px !important;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    #toast-container {
        max-width: 600px !important;
    }
    
    .toast {
        max-width: 600px !important;
    }
}

/* تأثيرات الحركة المحسنة */
.toast {
    animation: slideInDown 0.3s ease-out !important;
}

@keyframes slideInDown {
    from {
        transform: translate3d(-50%, -100%, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(-50%, 0, 0);
        opacity: 1;
    }
}

/* تأثير الاختفاء */
.toast.ng-leave {
    animation: slideOutUp 0.3s ease-in !important;
}

@keyframes slideOutUp {
    from {
        transform: translate3d(-50%, 0, 0);
        opacity: 1;
    }
    to {
        transform: translate3d(-50%, -100%, 0);
        opacity: 0;
    }
}

/* تحسينات إضافية للنصوص العربية */
.toast-message,
.toast-title {
    direction: rtl !important;
    text-align: center !important;
}

/* تحسين الأيقونات */
.toast .zmdi,
.toast .fa,
.toast .icon {
    margin-left: 8px !important;
    font-size: 16px !important;
    vertical-align: middle !important;
}

/* تحسين الروابط داخل الإشعارات */
.toast a {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: underline !important;
}

.toast a:hover {
    color: #fff !important;
}
