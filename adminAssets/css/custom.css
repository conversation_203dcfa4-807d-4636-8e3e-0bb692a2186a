ul.list-group {
  padding-right: 0;
}

ul.list-group .list-group-item {
  padding: 10px;
}

ul.list-group .media img {
  float: right;
}

ul.list-group .media .media-body {
  float: right;
  width: calc(100% - 85px);
}

.box .box-body {
  padding: 10px;
}

.box .box-head {
  padding: 8px 10px;
  border-bottom: 1px solid #f1f1f1;
  background: #f4f4f4;
}

.box .box-head button {
  font-size: 16px !important;
  line-height: 14px;
}

.header-logo {
  height: 50px;
}

.side-header {
  top: 50px;
  height: calc(100vh - 50px);
}

.button-xs {
  font-weight: bold;
}

.table .adomx-checkbox {
  padding: 0px;
  min-width: 10px;
}

.table input[type="checkbox"] {
  margin: 0;
}

.table .post-title {
  float: right;
  min-width: 300px;
}

.table a.txt-cont-img {
  float: right;
  width: calc(100% - 100px);
  min-height: 56px;
  padding: 0 10px;
  font-weight: bold;
  font-size: 14px;
  display: block;
  overflow: hidden;
  white-space: initial;
}

.table a.txt-cont-img .button {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .table a.txt-cont-img {
    font-size: 16px;
  }
}

@media (min-width: 992px) {
  .table a.txt-cont-img {
    font-size: 18px;
  }
}

.table .stat-cont {
  float: right;
  width: 100%;
}

.table .img-cont {
  float: right;
  width: 100px;
}

.table .img-cont img {
  width: 100%;
  height: auto;
}

.table tr.selected {
  background-color: rgba(255, 150, 102, 0.1);
}
/*# sourceMappingURL=custom.css.map */