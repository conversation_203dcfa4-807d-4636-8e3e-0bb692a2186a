$( function() {

    /* The Combobox of select2 functions */
    

    //First the categories/subCategories functions
    //set the GET data that will be sent for the initial request
    var data = {"term" : $("#cat").val()};
    //set the subCat dropdown menu to be select2 with data of the default main cat value
    $("#subCat").select2({
            ajax: {
              url: url+'/admin/comboBox/cats',
              dataType: 'json',
              data : data              
            },
            //make the select options always open even after choosing one
            closeOnSelect: false
        });

    //check if the main cat dropdown menu has changed
    $('#cat').on('change', function (e) {
        //reset the GET data of term to be set according to the new change of Main category
        var data = {"term" : $(this).val()};
        $("#subCat").select2({
            ajax: {
              url: url+'/admin/comboBox/cats',
              dataType: 'json',
              data : data             
            },
            //make the select options always open even after choosing one
            closeOnSelect: false
        })
    });


    //Second the Inner News functions
    //make auto complete/multi choose of the inner news input
    $( "#inner" ).select2({
          //set the maximum limit to select up to 6 news
          maximumSelectionLength: 6,
          ajax: {
            url: url+'/admin/comboBox/news',
            dataType: 'json'
          }
      });



    //Third the news props functions
    //make the props input always on
    $("#newsProps").select2({
        closeOnSelect: false
    });



    //Fourth the News Tags/Related functions
    //make auto complete/multi choose of the news Tags input
    $( "#newsTags" ).select2({          
          ajax: {
            url: url+'/admin/comboBox/tags',
            dataType: 'json'
          },
           //make the select options always open even after choosing one
          closeOnSelect: false
      });

    var tagsData = {"term" : $("#newsTags").val().join(",")};
    //set the subCat dropdown menu to be select2 with data of the default main cat value
    $("#newsRelated").select2({
            ajax: {
              url: url+'/admin/comboBox/newsRelated',
              dataType: 'json',
              data : tagsData              
            },
            //make the select options always open even after choosing one
            closeOnSelect: false
        });
    //check if the News Tags is changed
    $( "#newsTags" ).on("change", function (e) {
        //set the tagsData to be array of tags imploded with ",""
         var tagsData = {"term" : $(this).val().join(",")};
         //set the the news related to get data according to the tags data
        $("#newsRelated").select2({
          //set the maximum limit to select up to 6 news
          maximumSelectionLength: 6,
            ajax: {
              url: url+'/admin/comboBox/newsRelated',
              dataType: 'json',
              data : tagsData              
            },
            //make the select options always open even after choosing one
            closeOnSelect: false
        });
    });
});


  //submit form function which set what step are you passing the news to :
  function submitForm(toStep){ 
    //where toStep is the step id and set it into the hidden field of toStep       
      $("#toStep").val(toStep);
  }