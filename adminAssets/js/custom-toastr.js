/**
 * إعدادات Toastr العامة للموقع بالكامل
 * Global Toastr Configuration
 */

// تهيئة إعدادات Toastr عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function () {
    // التحقق من وجود مكتبة Toastr
    if (typeof toastr !== "undefined") {
        // إعدادات Toastr الأساسية
        toastr.options = {
            closeButton: true,
            debug: false,
            newestOnTop: true,
            progressBar: true,
            positionClass: "toast-top-center", // الموقع في الأعلى في المنتصف
            preventDuplicates: false,
            onclick: null,
            showDuration: "300",
            hideDuration: "1000",
            timeOut: "5000",
            extendedTimeOut: "1000",
            showEasing: "swing",
            hideEasing: "linear",
            showMethod: "fadeIn",
            hideMethod: "fadeOut",
            tapToDismiss: true,
            escapeHtml: false,
        };
    }
});

// وظائف مساعدة لإظهار الإشعارات
window.showSuccessToast = function (message, title = "نجح!") {
    if (typeof toastr !== "undefined") {
        toastr.success(message, title);
    }
};

window.showErrorToast = function (message, title = "خطأ!") {
    if (typeof toastr !== "undefined") {
        toastr.error(message, title);
    }
};

window.showWarningToast = function (message, title = "تحذير!") {
    if (typeof toastr !== "undefined") {
        toastr.warning(message, title);
    }
};

window.showInfoToast = function (message, title = "معلومات") {
    if (typeof toastr !== "undefined") {
        toastr.info(message, title);
    }
};

// وظيفة لإظهار إشعار مخصص مع أيقونة
window.showCustomToast = function (type, message, title, icon = "") {
    if (typeof toastr !== "undefined") {
        const iconHtml = icon ? `<i class="${icon}"></i> ` : "";
        const fullTitle = iconHtml + title;

        switch (type) {
            case "success":
                toastr.success(message, fullTitle);
                break;
            case "error":
                toastr.error(message, fullTitle);
                break;
            case "warning":
                toastr.warning(message, fullTitle);
                break;
            case "info":
                toastr.info(message, fullTitle);
                break;
            default:
                toastr.info(message, fullTitle);
        }
    }
};

// وظيفة لإظهار إشعار الباركود (للاستخدام في صفحات الباركود)
window.showBarcodeSuccessToast = function (policyNo, clientName, policyValue) {
    const message = `تم استلام الطلب بنجاح\nرقم البوليصة: ${policyNo}\nالعميل: ${clientName}\nالقيمة: ${policyValue} جنيه`;
    showCustomToast(
        "success",
        message,
        "نجح الاستلام!",
        "zmdi zmdi-check-circle"
    );
};

window.showBarcodeErrorToast = function (policyNo) {
    const message = `لم يتم العثور على طلب برقم البوليصة: ${policyNo}`;
    showCustomToast("error", message, "خطأ!", "zmdi zmdi-alert-triangle");
};

// وظائف خاصة بتعيين المندوبين
window.showAssignmentSuccessToast = function (
    policyNo,
    clientName,
    deliveryManName,
    policyValue
) {
    const message = `تم تعيين الطلب للمندوب بنجاح\nرقم البوليصة: ${policyNo}\nالعميل: ${clientName}\nالمندوب: ${deliveryManName}\nالقيمة: ${policyValue} جنيه`;
    showCustomToast(
        "success",
        message,
        "نجح التعيين!",
        "zmdi zmdi-assignment-check"
    );
};

window.showAssignmentErrorToast = function (policyNo) {
    const message = `لم يتم العثور على طلب متاح برقم البوليصة: ${policyNo}`;
    showCustomToast(
        "error",
        message,
        "خطأ في التعيين!",
        "zmdi zmdi-assignment-alert"
    );
};

// وظائف التحذير من التعيين المسبق
window.showAlreadyAssignedSameToast = function (
    policyNo,
    clientName,
    deliveryManName,
    assignedDate
) {
    const message = `هذا الطلب مُعيَّن بالفعل لنفس المندوب المختار\nرقم البوليصة: ${policyNo}\nالعميل: ${clientName}\nالمندوب: ${deliveryManName}\nتاريخ التعيين: ${assignedDate}`;
    showCustomToast("warning", message, "مُعيَّن مسبقاً!", "zmdi zmdi-info");
};

window.showAlreadyAssignedOtherToast = function (
    policyNo,
    clientName,
    deliveryManName,
    assignedDate
) {
    const message = `هذا الطلب مُعيَّن بالفعل لمندوب آخر\nرقم البوليصة: ${policyNo}\nالعميل: ${clientName}\nالمندوب المُعيَّن: ${deliveryManName}\nتاريخ التعيين: ${assignedDate}`;
    showCustomToast(
        "warning",
        message,
        "مُعيَّن لمندوب آخر!",
        "zmdi zmdi-alert-circle"
    );
};

// وظيفة لإظهار إشعار مع مدة مخصصة
window.showTimedToast = function (type, message, title, duration = 5000) {
    if (typeof toastr !== "undefined") {
        // حفظ الإعدادات الحالية
        const originalTimeOut = toastr.options.timeOut;

        // تطبيق المدة المخصصة
        toastr.options.timeOut = duration;

        // إظهار الإشعار
        switch (type) {
            case "success":
                toastr.success(message, title);
                break;
            case "error":
                toastr.error(message, title);
                break;
            case "warning":
                toastr.warning(message, title);
                break;
            case "info":
                toastr.info(message, title);
                break;
        }

        // إعادة الإعدادات الأصلية
        toastr.options.timeOut = originalTimeOut;
    }
};

// وظيفة لمسح جميع الإشعارات
window.clearAllToasts = function () {
    if (typeof toastr !== "undefined") {
        toastr.clear();
    }
};

// وظيفة لإظهار إشعار دائم (لا يختفي تلقائياً)
window.showPersistentToast = function (type, message, title) {
    if (typeof toastr !== "undefined") {
        // حفظ الإعدادات الحالية
        const originalTimeOut = toastr.options.timeOut;
        const originalExtendedTimeOut = toastr.options.extendedTimeOut;

        // تطبيق إعدادات الإشعار الدائم
        toastr.options.timeOut = 0;
        toastr.options.extendedTimeOut = 0;

        // إظهار الإشعار
        switch (type) {
            case "success":
                toastr.success(message, title);
                break;
            case "error":
                toastr.error(message, title);
                break;
            case "warning":
                toastr.warning(message, title);
                break;
            case "info":
                toastr.info(message, title);
                break;
        }

        // إعادة الإعدادات الأصلية
        toastr.options.timeOut = originalTimeOut;
        toastr.options.extendedTimeOut = originalExtendedTimeOut;
    }
};

// تهيئة إضافية للـ Livewire إذا كان متاحاً
if (typeof Livewire !== "undefined") {
    // إعادة تطبيق إعدادات Toastr بعد تحديث Livewire
    Livewire.hook("message.processed", (message, component) => {
        if (typeof toastr !== "undefined") {
            toastr.options.positionClass = "toast-top-center";
        }
    });
}

// تصدير الوظائف للاستخدام العام
window.ToastrHelper = {
    success: showSuccessToast,
    error: showErrorToast,
    warning: showWarningToast,
    info: showInfoToast,
    custom: showCustomToast,
    barcodeSuccess: showBarcodeSuccessToast,
    barcodeError: showBarcodeErrorToast,
    assignmentSuccess: showAssignmentSuccessToast,
    assignmentError: showAssignmentErrorToast,
    alreadyAssignedSame: showAlreadyAssignedSameToast,
    alreadyAssignedOther: showAlreadyAssignedOtherToast,
    timed: showTimedToast,
    clear: clearAllToasts,
    persistent: showPersistentToast,
};
