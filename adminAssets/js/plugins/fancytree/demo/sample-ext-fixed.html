<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Fixed Headers</title>

	<link rel="stylesheet" type="text/css" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
	<script src="//code.jquery.com/jquery-3.4.1.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<style type="text/css">
	/* Set alternating row colors (define BEFORE standard css). */
/*
	table.fancytree-ext-table tbody tr:nth-child(even){
		background-color: #f4f4f8;
	}
*/
	/* custom alignment (set by 'renderColumns'' event) */
/* 	td.alignRight {
	   text-align: right;
	} */

	table th {
		background-color: lightgrey;
	}

	td, th {
		/* height: 30px; */
		border: 1px solid #ededed;
		text-align: center;
		vertical-align: middle;
		padding: 5px;
	   white-space: pre;
	}
	td:nth-child(2) {
		text-align: left;
	}

	table {
		border-collapse: collapse;
	}

	#tableWrapper {
	   width: 100%;
	   height: 600px;
	   overflow: hidden;
	   position: relative;
	}

	table.fancytree-container.fancytree-ext-fixed tbody tr {
		height: 22px;
	}

	table.fancytree-container.fancytree-ext-fixed tbody tr td:nth-child(1) {
		width: 22px;
	}

	table.fancytree-ext-table tbody tr.fancytree-ext-fixed-hover {
	  border-color: inherit;
	  background: #f8fcfe;
	  color: inherit;
	  background: -moz-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
	  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f8fcfe), color-stop(100%, #eff9fe));
	  background: -webkit-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
	  background: -o-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
	  background: -ms-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
	  background: linear-gradient(to bottom, #f8fcfe 0%, #eff9fe 100%);
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f8fcfe', endColorstr='#eff9fe', GradientType=0);
	  outline: 1px solid #d8f0fa;
	}

	table.fancytree-ext-table tbody tr.fancytree-active.fancytree-ext-fixed-hover,
	table.fancytree-ext-table tbody tr.fancytree-selected.fancytree-ext-fixed-hover {
	  border-color: inherit;
	  background: #f2f9fd;
	  color: inherit;
	  background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f2f9fd), color-stop(100%, #c4e8fa));
	  background: -webkit-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -o-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -ms-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: linear-gradient(to bottom, #f2f9fd 0%, #c4e8fa 100%);
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2f9fd', endColorstr='#c4e8fa', GradientType=0);
	  outline: 1px solid #B6E6FB;
	}

	table.fancytree-ext-table {
		width: 100%;
	}

	table.fancytree-ext-table tbody tr.fancytree-active.fancytree-ext-fixed-hover,
	table.fancytree-ext-table tbody tr.fancytree-selected.fancytree-ext-fixed-hover {
	  border-color: inherit;
	  background: #f2f9fd;
	  color: inherit;
	  background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f2f9fd), color-stop(100%, #c4e8fa));
	  background: -webkit-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -o-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -ms-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: linear-gradient(to bottom, #f2f9fd 0%, #c4e8fa 100%);
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2f9fd', endColorstr='#c4e8fa', GradientType=0);
	  outline: 1px solid #B6E6FB;
	}

	table.fancytree-ext-table {
		width: 100%;
	}

	table.fancytree-ext-table tbody tr.fancytree-active.fancytree-ext-fixed-hover,
	table.fancytree-ext-table tbody tr.fancytree-selected.fancytree-ext-fixed-hover {
	  border-color: inherit;
	  background: #f2f9fd;
	  color: inherit;
	  background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f2f9fd), color-stop(100%, #c4e8fa));
	  background: -webkit-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -o-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: -ms-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
	  background: linear-gradient(to bottom, #f2f9fd 0%, #c4e8fa 100%);
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2f9fd', endColorstr='#c4e8fa', GradientType=0);
	  outline: 1px solid #B6E6FB;
	}

</style>
	<link href="../src/skin-win7/ui.fancytree.css" rel="stylesheet" type="text/css">
	<script src="../src/jquery.fancytree.js" type="text/javascript"></script>
	<script src="../src/jquery.fancytree.table.js" type="text/javascript"></script>
	<script src="../src/jquery.fancytree.fixed.js" type="text/javascript"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js" type="text/javascript"></script>
	<link href="sample.css" rel="stylesheet" type="text/css">
	<script src="sample.js" type="text/javascript"></script>
	<!-- End_Exclude -->

<script type="text/javascript">
	$(function(){
		// Attach the fancytree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the fancytree() function:
		window.console.time("Fancytree - postProcess");
		window.console.time("Fancytree - init");
		$("#treetable").fancytree({
			extensions: ["table", "fixed"],
			checkbox: true,
			autoScroll: true,
			fixed: {
				fixCols: 4,	  // Fix leftmost n columns
//		        fixColWidths: [16, 50, 200],
				fixRows: 2  // Fix topmost n rows (true: whole <thead>)
			},
			table: {
				indentation: 20,	  // indent 20px per node level
				nodeColumnIdx: 2,	 // render the node title into the 2nd column
				checkboxColumnIdx: 0  // render the checkboxes into the 1st column
			},
			source: {
				url: "ajax-tree-mass-data.json"
			},
			postProcess: function(event, data) {
				window.console.timeEnd("Fancytree - postProcess");
			},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			},
			init: function(event, data) {
				window.console.timeEnd("Fancytree - init");
				window.console.info("node count:" + data.tree.count() + ", tr count: " + data.tree.$container.find("tbody>tr").length);
				$("#fixedRows").val(data.options.fixed.fixRows);
				$("#fixedCols").val(data.options.fixed.fixCols);
			},
			renderColumns: function(event, data) {
				var node = data.node,
					$tr = $(node.tr),
					$tdList = $tr.find(">td"),
					idx = node.getIndexHier();
				// (index #0 is rendered by fancytree by adding the checkbox)
				$tdList.eq(1).text(idx).addClass("alignRight");
				// (index #2 is rendered by fancytree)
				$tdList.eq(3).text(node.key);
				$tdList.eq(4).html("<input type='checkbox' name='like' value='" + node.key + "'>");

				for (var i = 5; i < 34; i++) {
					$tdList.eq(i).append($("<input>"));
				}
			},
			collapse: function(event, data) {
				data.node.discardMarkup(false);
			}
		});
	});
</script>

<!-- Start_Exclude: This block is not part of the sample code -->
<script type="text/javascript">
	$(function(){
		/* Handle custom checkbox clicks */
		$("#treetable").on("click", "input[name=like]", function(e){
			var node = $.ui.fancytree.getNode(e),
			   $input = $(e.target);
			e.stopPropagation();  // prevent fancytree activate for this row
			if($input.is(":checked")){
				alert("like " + $input.val());
			}else{
				alert("dislike " + $input.val());
			}
		});
		$("#fixedRows").on("keyup", function(evt) {
			var rows = parseInt($(this).val());
			if (!isNaN(rows) && evt.keyCode === 13) {
				var tree = $.ui.fancytree.getTree("#treetable");
				tree.options.fixed.fixRows = rows;
				tree.reload();
			}
		});
		$("#fixedCols").on("keyup", function(evt) {
			var cols = parseInt($(this).val());
			if (!isNaN(cols) && evt.keyCode === 13) {
				var tree = $.ui.fancytree.getTree("#treetable");
				tree.options.fixed.fixCols = cols;
				tree.reload();
			}
		});

		addSampleButton({
			label: "Expand all",
			code: function(){
//				var tree = $.ui.fancytree.getTree();
				var $tree = $("#treetable");
				var tree = $.ui.fancytree.getTree($tree);

				window.console.time("expand all");
				tree.enableUpdate(false);
				tree.visit(function(n){
					n.setExpanded();
				});
				tree.enableUpdate(true);
				window.console.timeEnd("expand all");
			}
		});
		addSampleButton({
			label: "Collapse all",
			newline: false,
			code: function(){
				window.console.time("collapse all");
				$.ui.fancytree.getTree("#treetable").visit(function(n){
					n.setExpanded(false);
				});
				window.console.timeEnd("collapse all");
			}
		});
	});
</script>
<!-- End_Exclude -->

</head>

<body class="example">
	<h1>Example: 'fixed' extension</h1>
	<div class="description">
		<p>
			Provide fixed table headers and columns for the table extension.
		</p>
		<p>
			<b>Status:</b> experimental / work in progress.
			<b>Details:</b>
			<a href="https://github.com/mar10/fancytree/wiki/ExtFixed"
				target="_blank" class="external">ext-fixed</a>.
		</p>
	</div>

	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
		<label for="fixedCols">Fixed Columns</label> <input id="fixedCols">
		<label for="fixedRows">Fixed Rows:</label> <input id="fixedRows">
	</div>

	<p id="sampleButtons">
	</p>
	<!-- Add a <table> element where the tree should appear: -->
	<div id="tableWrapper">
		<table id="treetable">
<!-- 			<colgroup>
			<col width="30px"></col>
			<col width="30px"></col>
			<col width="*"></col>
			<col width="50px"></col>
			<col width="30px"></col>
			</colgroup> -->
			<thead>
				<tr>
					<th></th>
					<th>#</th>
					<th>Title</th>
					<th>Key</th>
					<th>Head R1 C5</th>
					<th>Head R1 C6</th>
					<th>Head R1 C7</th>
					<th>Head R1 C8</th>
					<th>Head R1 C9</th>
					<th>Head R1 C10</th>
					<th>Head R1 C11</th>
					<th>Head R1 C12</th>
					<th>Head R1 C13</th>
					<th>Head R1 C14</th>
					<th>Head R1 C15</th>
					<th>Head R1 C16</th>
					<th>Head R1 C17</th>
					<th>Head R1 C18</th>
					<th>Head R1 C19</th>
					<th>Head R1 C20</th>
					<th>Head R1 C21</th>
					<th>Head R1 C22</th>
					<th>Head R1 C23</th>
					<th>Head R1 C24</th>
					<th>Head R1 C25</th>
					<th>Head R1 C26</th>
					<th>Head R1 C27</th>
					<th>Head R1 C28</th>
					<th>Head R1 C29</th>
					<th>Head R1 C30</th>
					<th>Head R1 C31</th>
					<th>Head R1 C32</th>
					<th>Head R1 C33</th>
					<th>Head R1 C34</th>
				</tr>
				<tr>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th>Head R2 C5</th>
					<th>Head R2 C6</th>
					<th>Head R2 C7</th>
					<th>Head R2 C8</th>
					<th>Head R2 C9</th>
					<th>Head R2 C10</th>
					<th>Head R2 C11</th>
					<th>Head R2 C12</th>
					<th>Head R2 C13</th>
					<th>Head R2 C14</th>
					<th>Head R2 C15</th>
					<th>Head R2 C16</th>
					<th>Head R2 C17</th>
					<th>Head R2 C18</th>
					<th>Head R2 C19</th>
					<th>Head R2 C20</th>
					<th>Head R2 C21</th>
					<th>Head R2 C22</th>
					<th>Head R2 C23</th>
					<th>Head R2 C24</th>
					<th>Head R2 C25</th>
					<th>Head R2 C26</th>
					<th>Head R2 C27</th>
					<th>Head R2 C28</th>
					<th>Head R2 C29</th>
					<th>Head R2 C30</th>
					<th>Head R2 C31</th>
					<th>Head R2 C32</th>
					<th>Head R2 C33</th>
					<th>Head R2 C34</th>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</table>
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<p>
	   <dl>
		   <dt>2016-12-23</dt>
		   <dd>13 Sek.</dd>
	   </dl>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
