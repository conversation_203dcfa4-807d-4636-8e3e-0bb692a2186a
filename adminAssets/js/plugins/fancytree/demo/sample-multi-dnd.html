<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Test D'n'D - Fancytree</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.dnd.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="../demo/sample.css" rel="stylesheet">
	<script src="../demo/sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">
	.draggable,
	.droppable {
		border: 1px solid green;
		padding: 3px;
		background-color: silver;
	}
	/* Prevent scrolling while DND */
	ul.fancytree-container {
/*
		height: 200px;
		overflow: auto;
*/
/*		position: inherit;*/
	}
</style>

<script type="text/javascript">
	$(function(){
		var count = 1;
		/*
		TODO: option 'preventTextSelect'?
			Already implemented, but doesn't always work:
				}).on("selectstart" + ns, "span.fancytree-title", function(event){
					// prevent mouse-drags to select text ranges
					// tree.debug("<span title> got event " + event.type);
					event.preventDefault();
		TODO: disable auto-scroll by default:
		  seems to have problems to calculate helper position,
		  --> see here http://api.jqueryui.com/draggable/#event-drag
			  for a possible fix?
		  and enabling scrolling would always require custom changes, like
		  setting the container height?
		TODO: Revert always flies to top-left corner of container
		*/
		// Attach the fancytree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the fancytree() function:
		$("#tree").fancytree({
			extensions: ["dnd"],
			checkbox: true,
//			debugLevel: 3,
			source: {
				url: "ajax-tree-plain.json"
			},
			activate: function(event, data) {
			},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			},
			dnd: {
				preventVoidMoves: true, // Prevent dropping nodes 'before self', etc.
				preventRecursiveMoves: true, // Prevent dropping nodes on own descendants
				autoExpandMS: 400,
				// focusOnClick: true,
				refreshPositions: true,
				draggable: {
					appendTo: "body",  // We don't want to clip the helper inside container
					// scroll: false,
					// containment: "parent",  // $("ul.fancytree-container"),
					// cursorAt: { left: 5 },
					revert: "invalid"
					// revert: function(dropped) {
					// 	return
					// }
				},
				dragStart: function(node, data) {
					// allow dragging `node`:
					return true;
				},
				dragEnter: function(node, data) {
				   return true;
				},
				initHelper: function(node, data) {
					// Helper was just created: modify markup
					var helper = data.ui.helper,
						sourceNodes = data.tree.getSelectedNodes();

					// Store a list of active + all selected nodes
					if( !node.isSelected() ) {
						sourceNodes.unshift(node);
					}
					helper.data("sourceNodes", sourceNodes);
					// Mark selected nodes also as drag source (active node is already)
					$(".fancytree-active,.fancytree-selected", tree.$container)
						.addClass("fancytree-drag-source");
					// Add a counter badge to helper if dragging more than one node
					if( sourceNodes.length > 1 ) {
						helper.append($("<span class='fancytree-childcounter'/>")
							.text("+" + (sourceNodes.length - 1)));
					}
					// Prepare an indicator for copy-mode
					helper.prepend($("<span class='fancytree-dnd-modifier'/>")
						.text("+").hide());
				},
				updateHelper: function(node, data) {
					// Mouse was moved or key pressed: update helper according to modifiers

					// NOTE: pressing modifier keys stops dragging in jQueryUI 1.11
					// http://bugs.jqueryui.com/ticket/14461
					var event = data.originalEvent,
						tree = node.tree,
						copyMode = event.ctrlKey || event.altKey;

					// Adjust the drop marker icon
//					data.dropMarker.toggleClass("fancytree-drop-copy", copyMode);

					// Show/hide the helper's copy indicator (+)
					data.ui.helper.find(".fancytree-dnd-modifier").toggle(copyMode);
					// tree.debug("1", $(".fancytree-active,.fancytree-selected", tree.$container).length)
					// tree.debug("2", $(".fancytree-active,.fancytree-selected").length)
					// Dim the source node(s) in move-mode
					$(".fancytree-drag-source", tree.$container)
						.toggleClass("fancytree-drag-remove", !copyMode);
					// data.dropMarker.toggleClass("fancytree-drop-move", !copyMode);
				},
				dragDrop: function(node, data) {
					var sourceNodes = data.ui.helper.data("sourceNodes"),
						event = data.originalEvent,
						copyMode = event.ctrlKey || event.altKey;

					if( copyMode ) {
						$.each(sourceNodes, function(i, o){
							o.copyTo(node, data.hitMode, function(n){
								delete n.key;
								n.selected = false;
								n.title = "Copy of " + n.title;
							});
						});
					}else{
						$.each(sourceNodes, function(i, o){
							o.moveTo(node, data.hitMode);
						});
					}
				}
			}
		});

		$(".droppable").droppable({
			drop: function(event, ui){
				var node = $(ui.helper).data("ftSourceNode"),
					tree = node.tree,
					copyMode = event.ctrlKey || event.altKey,
					sourceNodes = ui.helper.data("sourceNodes");

				if( !copyMode ) {
					$.each(sourceNodes, function(i, o){
						o.remove();
					});
				}
				$(this).append((copyMode ? "Copied " : "Moved ") + sourceNodes.length + " nodes. ");
			}
		});
	});
	</script>
</head>
<body class="example">
	<h1>Example: extended drag'n'drop sample</h1>
	<div class="description">
		This sample shows how to
		<ul>
		<li>implement drag'n'drop with multiple selected nodes
		<li>allow modifier keys <kbd>Ctrl</kbd> or <kbd>Alt</kbd> to force copy
			instead of move operations
		</ul>
	</div>
	<p class="warning">
		This sample uses the jQuery UI based 'ext-dnd' extension.<br>
		See also the <a href="sample-multi-dnd5.html">ext-dnd5 sample</a>.
	</p>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<!-- Add a <table> element where the tree should appear: -->
	<p class="description">
		Standard tree:
	</p>
	<div id="tree"></div>

	<p class="droppable">
		Droppable.
	</p>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
