<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Webservice Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

	<script type="text/javascript">
		$(function(){
			$("#taxonTree").fancytree({
				source: {
					url: "//www.itis.gov/ITISWebService/jsonservice/getKingdomNames",
					data: {
						jsonp: "itis_data"
					},
					cache: true,
					jsonpCallback: "itis_data",
					dataType: "jsonp"
				},
				lazyLoad: function(event, data) {
					data.result = {
						url: "//www.itis.gov/ITISWebService/jsonservice/getHierarchyDownFromTSN",
						data: {
							jsonp: "itis_data",
							tsn: data.node.key
						},
						cache: true,
						jsonpCallback: "itis_data",
						dataType: "jsonp"
					};
				},
				postProcess: function(event, data) {
					// Convert incoming ITIS format to native Fancytree
					var response = data.response;

					data.node.info(response);
					switch( response.class ) {
					case "gov.usgs.itis.itis_service.metadata.SvcKingdomNameList":
						data.result = $.map(response.kingdomNames, function(o){
							return o && {title: o.kingdomName, key: o.tsn, folder: true, lazy: true};
						});
						break;
					case "gov.usgs.itis.itis_service.data.SvcHierarchyRecordList":
						data.result = $.map(response.hierarchyList, function(o){
							return o && {title: o.taxonName, key: o.tsn, folder: true, lazy: true};
						});
						break;
					default:
						$.error("Unsupported class: " + response.class);
					}
				}
			});
		});
	</script>
<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	addSampleButton({
		label: "Goto 'Homo Sapiens'",
		code: function(){
			var tree = $.ui.fancytree.getTree("#taxonTree"),
				path = "/202423/914154/914156/158852/331030/914179/914181/179913/179916/179925/180089/943773/943778/943782/180090/943805/180091/180092";

			tree.loadKeyPath(path, function(node, status){
				switch( status ) {
				case "loaded":
					node.makeVisible();
					break;
				case "ok":
					node.setActive();
					break;
				}
			});
		}
	});
});
</script>
<!-- End_Exclude -->
</head>

<body class="example">
	<h1>Example: Accessing external Webservices</h1>
	<div class="description">
		Load realtime data from the public
		<a href="http://www.itis.gov/web_service.html" target="_blank" class="external">
			<abbr title="Integrated Taxonomic Information System">ITIS</abbr>
			Web Services
		</a>
		and convert the provided JSON data to Fancytree format.
		<br>
		See the <a href="https://wwWendt.de/tech/fancytree/demo/taxonomy-browser/">Taxonomy Browser</a>
		for a more sophisticated example.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<p id="sampleButtons">
	</p>
	<fieldset>
		<legend>Integrated Taxonomic Information System</legend>
		<div id="taxonTree">
		</div>
	</fieldset>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
