<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<script type="text/javascript">
$(function(){
	// Initialize the tree inside the <div>element.
	// The tree structure is read from the contained <ul> tag.
	$("#tree").fancytree({
		checkbox: true,
		activate: function(event, data) {
			$("#echoActive").text(data.node.title);
//              alert(node.getKeyPath());
			if( data.node.url )
				window.open(data.node.url, data.node.target);
		},
		deactivate: function(event, data) {
			$("#echoSelected").text("-");
		},
		focus: function(event, data) {
			$("#echoFocused").text(data.node.title);
		},
		blur: function(event, data) {
			$("#echoFocused").text("-");
		},
		lazyLoad: function(event, data){
			// Simulate a slow Ajax request
			var dfd = new $.Deferred();
			data.result = dfd.promise();
			window.setTimeout(function(){
				dfd.resolve([
					{ title: "Lazy node 1", lazy: true },
					{ title: "Simple node 2", select: true }
				]);
			}, 1500);
		}
	});
});
</script>

<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	addSampleButton({
		label: "Disable",
		id: "btnDisable",
		code: function(){
			var tree = $.ui.fancytree.getTree("#tree"),
				wasEnabled = !tree.options.disabled;

			tree.enable(!wasEnabled);
			$("#btnDisable").text(wasEnabled ? "Enable" : "Disable");
		}
	});
	addSampleButton({
		label: "Expand all",
		newline: false,
		code: function(){
			$.ui.fancytree.getTree("#tree").expandAll();
		}
	});
	addSampleButton({
		label: "Collapse all",
		newline: false,
		code: function(){
			$.ui.fancytree.getTree("#tree").expandAll(false);
		}
	});
	addSampleButton({
		label: "Toggle expand",
		code: function(){
			$.ui.fancytree.getTree("#tree").visit(function(node){
				node.toggleExpanded();
			});
		}
	});
	addSampleButton({
		label: "tree.getActiveNode()",
		newline: false,
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			if( node ){
				alert("Currently active: " + node.title);
			}else{
				alert("No active node.");
			}
		}
	});
	addSampleButton({
		label: "tree.toDict()",
		code: function(){
			// Convert the whole tree into an dictionary
			var tree = $.ui.fancytree.getTree("#tree");
			var d = tree.toDict(true);
			alert(JSON.stringify(d));
		}
	});
	addSampleButton({
		label: "activateKey('id4.3.2')",
		code: function(){
			$.ui.fancytree.getTree("#tree").activateKey("id4.3.2");
			// also possible:
//	              $.ui.fancytree.getTree("#tree").getNodeByKey("id4.3.2").setActive();
		}
	});
	addSampleButton({
		label: "setTitle()",
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			if( !node ) return;
			node.setTitle(node.title + ", " + new Date());
			// this is a shortcut for
			// node.fromDict({title: data.node.title + new Date()});
		}
	});
	addSampleButton({
		label: "Sort tree",
		newline: false,
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getRootNode();
			node.sortChildren(null, true);
		}
	});
	addSampleButton({
		label: "Sort active banch",
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			// Custom compare function (optional) that sorts case insensitive
			var cmp = function(a, b) {
				a = a.title.toLowerCase();
				b = b.title.toLowerCase();
				return a > b ? 1 : a < b ? -1 : 0;
			};
			node.sortChildren(cmp, false);
		}
	});
	addSampleButton({
		header: "Create nodes",
		tooltip: "Use node.addChildren() with single objects",
		label: "Add single nodes",
		newline: false,
		code: function(){
			// Sample: add an hierarchic branch using code.
			// This is how we would add tree nodes programatically
			var rootNode = $.ui.fancytree.getTree("#tree").getRootNode();
			var childNode = rootNode.addChildren({
				title: "Programatically addded nodes",
				tooltip: "This folder and all child nodes were added programmatically.",
				folder: true
			});
			childNode.addChildren({
				title: "Document using a custom icon",
				icon: "customdoc1.gif"
			});
		}
	});
	addSampleButton({
		tooltip: "Use node.appendSibling()",
		label: "Apppend a sibling node",
		newline: false,
		code: function(){
			var tree = $.ui.fancytree.getTree("#tree"),
				node = tree.getActiveNode(),
				newData = {title: "New Node"},
				newSibling = node.appendSibling(newData);
		}
	});
	addSampleButton({
		label: "ROOT.addChildren()",
		tooltip: "Use node.addChildren() with recursive arrays",
		code: function(){
			// Sample: add an hierarchic branch using an array
			var obj = [
				{ title: "Lazy node 1", lazy: true },
				{ title: "Lazy node 2", lazy: true },
				{ title: "Folder node 3", folder: true,
					children: [
						{ title: "node 3.1" },
						{ title: "node 3.2",
							children: [
								{ title: "node 3.2.1" },
								{ title: "node 3.2.2",
									children: [
										{ title: "node *******" }
									]
								}
							]
						}
					]
				}
			];
			$.ui.fancytree.getTree("#tree").getRootNode().addChildren(obj);
		}
	});
	addSampleButton({
		label: "node.fromDict()",
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			if( !node ) return;
			// Set node data and - optionally - replace children
			node.fromDict({
				title: node.title + new Date(),
				children: [{title: "t1"}, {title: "t2"}]
			});
		}
	});
	CLIPBOARD = null;
	addSampleButton({
		label: "Clipboard = node.toDict()",
		newline: false,
		code: function(){
			// Convert active node (and descendants) to a dictionary and store
			// in
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			var d = node.toDict(true, function(dict, node){
				// Remove keys, so they will be re-generated when this dict is
				// passed to addChildren()
				delete dict.key;
			});
			// Store in a globael variable
			CLIPBOARD = d;
			alert("CLIPBOARD = " + JSON.stringify(d));
		}
	});
	addSampleButton({
		label: "node.fromDict(Clipboard)",
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			if( !node ) return;
			// Set node data and - optionally - replace children
			node.fromDict(CLIPBOARD);
		}
	});
	addSampleButton({
		label: "Remove selected nodes (but keep children)",
		newline: true,
		code: function(){
			var tree = $.ui.fancytree.getTree("#tree"),
				selNodes = tree.getSelectedNodes();

			selNodes.forEach(function(node) {
				while( node.hasChildren() ) {
					node.getFirstChild().moveTo(node.parent, "child");
				}
				node.remove();
			});
		}
	});
});
</script>
<!-- End_Exclude -->

</head>

<body class="example">
	<h1>Fancytree API</h1>
	<div class="description">
		Demonstrate some Fancytree and FancytreeNode API methods.
		<br>
		See the <a href="https://github.com/mar10/fancytree/wiki/TutorialApi"
			target="_blank" class="external">API Tutorial</a>
		for details.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>

	<div id="tree">
		<ul>
			<li>This simple node (and the following) have been created from html.
			<li id="id1" title="This is item #1">item1 with key and tooltip
			<li id="id2">item2 with key "id2"

			<li id="id3" class="folder">Standard Folder with some children
				<ul>
					<li id="id3.1">Sub-item 3.1
					<li id="id3.2">Sub-item 3.2
				</ul>

			<li id="id4">item 4. Note that also non-folders (i.e. 'documents') may have child nodes
				<ul>
					<li id="id4.1">Sub-item 4.1
					<li id="id4.2">Sub-item 4.2
					<li id="id4.3">Sub-item 4.3
						<ul>
							<li id="id4.3.1">Sub-item 4.3.1
							<li id="id4.3.2">Sub-item 4.3.2
							<ul>
								<li id="id*******">Sub-item *******
								<li id="id*******">Sub-item *******
							</ul>
						</ul>
					<li id="id4.4">Sub-item 4.4
				</ul>

			<li id="id5" class="expanded folder">Advanced examples
				<ul>
					<li data="key: 'node5.1'">item5.1: Using data attribute as an alternative way to specify a key.
					<li data="key: 'node5.3', folder: true">item5.1: Using data attribute as an alternative way to specify a folder.
					<li id="id5.2">Sub-item 5.2
					<li>Item without a key. Keys are optional (generated automatically), but may be used in the callbacks
				</ul>
		</ul>
	</div>

	<div>Active node: <span id="echoActive">-</span></div>
	<div>Focused node: <span id="echoFocused">-</span></div>
	<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
