<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

	<script type="text/javascript">
		$(function(){
			$("#skinswitcher")
				// .skinswitcher("option", "base", "../../src/")
				.skinswitcher("addChoices", [
					{name: "win8-xxl", value: "win8-xxl", href: "skin-win8-xxl/ui.fancytree.css"}
					])
				.skinswitcher("change", "win8-xxl");
			// using default options
			$("#tree").fancytree();
		});
	</script>
</head>

<body class="example">
	<h1>Example: Default</h1>
	<div class="description">
		This tree uses default options.<br>
		It is initialized from a hidden &lt;ul> element on this page.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<div id="tree">
		<ul id="treeData" style="display: none;">
			<li id="id1" title="Look, a tool tip!">item1 with key and tooltip
			<li id="id2">item2
			<li id="id3" class="folder">Folder with some children
				<ul>
					<li id="id3.1">Sub-item 3.1
						<ul>
							<li id="id3.1.1">Sub-item 3.1.1
							<li id="id3.1.2">Sub-item 3.1.2
						</ul>
					<li id="id3.2">Sub-item 3.2
						<ul>
							<li id="id3.2.1">Sub-item 3.2.1
							<li id="id3.2.2">Sub-item 3.2.2
						</ul>
				</ul>
			<li id="id4" class="expanded">Document with some children (expanded on init)
				<ul>
					<li id="id4.1"  class="active focused">Sub-item 4.1 (active and focus on init)
						<ul>
							<li id="id4.1.1">Sub-item 4.1.1
							<li id="id4.1.2">Sub-item 4.1.2
						</ul>
					<li id="id4.2">Sub-item 4.2
						<ul>
							<li id="id4.2.1">Sub-item 4.2.1
							<li id="id4.2.2">Sub-item 4.2.2
						</ul>
				</ul>
		</ul>
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
