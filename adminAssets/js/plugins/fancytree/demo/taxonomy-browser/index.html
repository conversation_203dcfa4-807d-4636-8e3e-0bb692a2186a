<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">

	<title>Taxonomy Browser</title>

	<!--
	NOTE: "Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 3"
	-->
	<script src="//code.jquery.com/jquery-1.12.4.min.js"></script>
	<!-- <script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script> -->

	<link rel="stylesheet" href="//netdna.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
	<script src="//netdna.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

	<script type="text/javascript">
	// Fix BBQ for jQuery >= 1.9 (see https://github.com/cowboy/jquery-bbq/pull/42)
	if( !$.browser ) {
		console.warn("Monkey-patching jQuery.browser.msie to get BBQ working");
		var ua = navigator.userAgent.toLowerCase();
		$.browser = { msie: /msie/.test(ua) && !/opera/.test(ua) };
	}
	</script>
	<script src="//cdnjs.cloudflare.com/ajax/libs/jquery.ba-bbq/1.2.1/jquery.ba-bbq.min.js"></script>

	<script src="//cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.5/handlebars.min.js"></script>

<!--
	<link href="//cdn.jsdelivr.net/jquery.truncate/0.1/jquery.truncate.min.js"
		rel="stylesheet">
	<script src="https://rawgit.com/tbasse/jquery-truncate/master/jquery.truncate.js"
		type="text/javascript"></script>
 -->
	<link href="//cdn.jsdelivr.net/npm/jquery.fancytree/dist/skin-bootstrap/ui.fancytree.min.css"
		rel="stylesheet">
<!--
	<link href="../../src/skin-bootstrap/ui.fancytree.css"
		rel="stylesheet">
-->

	<script src="//cdn.jsdelivr.net/npm/jquery.fancytree/dist/jquery.fancytree-all-deps.min.js"></script>
<!--
	<script src="../../src/jquery.fancytree.js"></script>
	<script src="../../src/jquery.fancytree.edit.js"></script>
	<script src="../../src/jquery.fancytree.filter.js"></script>
	<script src="../../src/jquery.fancytree.glyph.js"></script>
	<script src="../../src/jquery.fancytree.table.js"></script>
	<script src="../../src/jquery.fancytree.wide.js"></script>
-->

	<link href="style.css" rel="stylesheet">
	<script src="taxonomy-browser.js"></script>
</head>

<body>
	<div class="page-header">
		<h1>Taxonomy Browser
<!--
		<small><a href="https://wwWendt.de/tech/fancytree/demo/taxonomy-browser/">home</a></small>
-->
		</h1>
	</div>
	<div class="panel panel-default">
		<div class="panel-heading">
			<h3 class="panel-title">Search <acronym title="Global Biodiversity Information Facility">GBIF</acronym></h3>
		</div>
		<div class="panel-body hidden-print">

			<input type="text" name="query" id="query"
				autocorrect="off" autocomplete="off"
				placeholder="Enter Search Phrase">
			<button id="btnResetSearch" class="btn btn-default btn-sm">&times;</button>
			<button id="btnSearch" class="btn btn-default btn-sm">Search</button>

			<div class="collapse" id="searchResultPane">
				<table id="searchResultTree"
					class="table table-striped table-hover table-condensed table-bordered">
					<colgroup>
					<col width="10em"></col>
					<col width="10em"></col>
					<col width="*"></col>
					<col width="30em"></col>
					<col width="30em"></col>
					<col width="30em"></col>
					<col width="30em"></col>
					<col width="30em"></col>
					<col width="10em"></col>
					<col width="10em"></col>
					<col width="30em"></col>
					<col width="30em"></col>
					</colgroup>
					<thead>
						<tr>
							<th class="visible-lg">Key</th>
							<th class="hidden-xs">Rank</th>
							<th>Scientific Name</th>
							<th class="hidden-xs hidden-sm">Vernacular  Names</th>
							<th class="hidden-xs hidden-sm">Canonical Name</th>
							<th class="visible-lg">According to</th>
							<th class="hidden-xs">Status</th>
							<th class="hidden-xs">Name Type</th>
							<th class="hidden-xs"># Occur.</th>
							<th class="hidden-xs"># Desc.</th>
							<th class="visible-lg">Author</th>
							<th class="visible-lg">Published in</th>
<!--
							canonicalName
							accordingTo
							extinct
							numDescendants
							numOccurrences
							publishedIn
							synonym
-->
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="visible-lg"/>
							<td class="hidden-xs" />
							<td/>
							<td class="hidden-xs hidden-sm" />
							<td class="hidden-xs hidden-sm" />
							<td class="visible-lg" />
							<td class="hidden-xs" />
							<td class="hidden-xs" />
							<td class="hidden-xs" />
							<td class="hidden-xs" />
							<td class="visible-lg" />
							<td class="visible-lg" />
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-4">
			<div class="btn-group">
				<button id="btnPin" class="btn btn-default btn-xs">Pin</button>
				<button id="btnUnpin" class="btn btn-default btn-xs">Unpin</button>
			</div>

			<div id="taxonTree" class="fancytree-fade-expander">
			</div>
		</div>
		<div class="col-md-8">
			<!-- Breadcrumb -->
			<ol class="breadcrumb">
				<li class="active">Please select an element.</li>
			</ol>

			<span id="tmplInfoPane">
			</span>
		</div>
	</div>

	<div class="panel panel-warning">
		<div class="panel-heading">
			Disclaimer
		</div>
		<div class="panel-body">
			<p>
				This site accesses data from external sources, namely the
				<a href="http://www.gbif.org/">Global Biodiversity Information Facility (GBIF)</a> database.
				There is no guarantee, that the display is correct, complete, or
				permanently available. Please refer to those original sources for
				authorative information.
			</p>
			<p>
				Copyright &copy; 2015 Martin Wendt. Created as a demo for
				<a href="https://github.com/mar10/fancytree">Fancytree</a>.
			</p>
		</div>
	</div>
</body>
</html>
