<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->
</head>

<body class="example">
	<h1>Example: Playground</h1>

	<div class="description">
		This tree is initialized from a data structure.
	</div>

	<p data-height="500" data-theme-id="0" data-slug-hash="mXgmXV" data-default-tab="js,result"
		data-user="mar10" data-embed-version="2" data-pen-title="Fancytree Demo" class="codepen">

		See the Pen <a href="https://codepen.io/mar10/pen/WMWrbq/">Fancytree Demo</a>
		by Martin Wendt (<a href="https://codepen.io/mar10">@mar10</a>)
		on <a href="https://codepen.io">CodePen</a>.
	</p>
	<script async src="https://static.codepen.io/assets/embed/ei.js"></script>
<!--
	<a class="jsbin-embed embedded-jsbin"
		href="//jsbin.com/boyiwo/embed?js,output&height=500px">
		JS Bin on jsbin.com</a>
	<script src="//static.jsbin.com/js/embed.min.js?3.41.9"></script>
-->

<!--
	<hr>

	<div class="description">
		This tree is initialized from a hidden &lt;ul> element on the page.
	</div>
	<a class="jsbin-embed embedded-jsbin"
		href="//jsbin.com/rerebov/embed?html,js,output&height=500px">
		JS Bin on jsbin.com</a>
	<script src="//static.jsbin.com/js/embed.min.js?3.41.9"></script>
-->
<!--
	<iframe class="embedded-plunkr"
		src="//embed.plnkr.co/XnFTEf?t=code&f=script.js"
		allowfullscreen="allowfullscreen">
		Loading plunk...
	</iframe>
-->

</body>
</html>
