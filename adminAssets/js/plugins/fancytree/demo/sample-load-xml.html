<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

	<script type="text/javascript">

		/** Return a list of NodeData objects, assuming $xml points to a list of
		 * nodes, e.g.:
		 *
		 * <children>
		 *   <node folder="true" expanded="true" key="id_1">
		 *     <title> Node 1</title>
		 *     <children>
		 *       <node key="id_1_1">
		 *         <title> Node 1.1</title>
		 *       <node>
		 *     </children>
		 *   </node>
		 *   <node folder="true" lazy="true" key="id_2">
		 *     <title> Node 2 (expanded folder)</title>
		 *   </node>
		 *
		 */
		function parseFancytreeXml($xml) {
			var children = [];

			$xml.children("node").each(function() {
				var $node = $(this),
					subnodes = $node.children("children");

				// Create Fancytree NodeData object from <node> element
				children.push({
					title: $node.children("title").text(),
					expanded: $node.attr("expanded"),
					folder: $node.attr("folder"),
					key: $node.attr("key"),
					lazy: $node.attr("lazy"),
					children: subnodes.length ? parseFancytreeXml(subnodes) : null
				});
			});
			return children;
		}

		$(function(){
			$("#tree").fancytree({
				// Explicitly pass XML as data type
				source: { url: "ajax-tree.xml", dataType: "xml" },
				lazyLoad: function(event, data) {
					// Lazy folders also request XML data
					data.result = { url: "ajax-sub.xml", dataType: "xml" };
				},
				postProcess: function(event, data) {
					// Convert the xml responses to a Fancytree NodeData list.
					// data.response is a `#document` root, so we get the outer
					// `<children>` element:
					data.result = parseFancytreeXml($(">children", data.response));
				}
			});
		});
	</script>
</head>

<body class="example">
	<h1>Example: Load XML</h1>
	<div class="description">
		This tree uses a custom XML parser.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<div id="tree">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
