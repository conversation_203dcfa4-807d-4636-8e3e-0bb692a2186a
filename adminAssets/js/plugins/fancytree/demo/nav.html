<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<meta name="robots" content="noindex,follow">
	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="sample.js"></script>
	<title>Fancytree - Example Browser Nav</title>

<style type="text/css">
body {
	background-color: #f7f7f7;
	/* background-color: #39414A;
	color: white; */
	font-family: Helvetica, Arial, sans-serif;
	font-size: smaller;
	/* background-image: url("nav_bg.png"); */
	/* background-repeat: repeat-x; */
}
div#tree {
	position: absolute;
	height: 95%;
	width: 95%;
	padding: 5px;
	margin-right: 16px;
}
ul.fancytree-container {
	height: 100%;
	width: 100%;
	overflow: auto;
	background-color: transparent;
}
span.fancytree-node span.fancytree-title {
	/* color: white; */
	text-decoration: none;
}
/* span.fancytree-focused span.fancytree-title {
	outline-color: white;
} */
span.fancytree-node:hover span.fancytree-title,
span.fancytree-active span.fancytree-title,
span.fancytree-active.fancytree-focused span.fancytree-title,
.fancytree-treefocus span.fancytree-title:hover,
.fancytree-treefocus span.fancytree-active span.fancytree-title {
	color: #39414A;
}
span.external span.fancytree-title:after {
	content: "";
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAMAAAC67D+PAAAAFVBMVEVmmcwzmcyZzP8AZswAZv////////9E6giVAAAAB3RSTlP///////8AGksDRgAAADhJREFUGFcly0ESAEAEA0Ei6/9P3sEcVB8kmrwFyni0bOeyyDpy9JTLEaOhQq7Ongf5FeMhHS/4AVnsAZubxDVmAAAAAElFTkSuQmCC") 100% 50% no-repeat;
	padding-right: 13px;
}
/* Remove system outline for focused container */
.ui-fancytree.fancytree-container:focus {
	outline: none;
}
.ui-fancytree.fancytree-container {
	border: none;
}
</style>

<script type="text/javascript">
$(function(){
	// --- Initialize sample trees
	$("#tree").fancytree({
		treeId: "nav",
		autoActivate: false, // we use scheduleAction()
		autoCollapse: true,
//			autoFocus: true,
		autoScroll: true,
		clickFolderMode: 3, // expand with single click
		minExpandLevel: 2,
		tabindex: "-1", // we don't want the focus frame
		// toggleEffect: { effect: "blind", options: {direction: "vertical", scale: "box"}, duration: 2000 },
		// scrollParent: null, // use $container
		tooltip: function(event, data) {
			return data.node.title;
		},
		focus: function(event, data) {
			var node = data.node;
			// Auto-activate focused node after 1 second
			if(node.data.href){
				node.scheduleAction("activate", 1000);
			}
		},
		blur: function(event, data) {
			data.node.scheduleAction("cancel");
		},
		beforeActivate: function(event, data){
			var node = data.node;

			if( node.data.href && node.data.target === "_blank") {
				window.open(node.data.href, "_blank");
				return false; // don't activate
			}
		},
		activate: function(event, data){
			var node = data.node,
				orgEvent = data.originalEvent || {};

			// Open href (force new window if Ctrl is pressed)
			if(node.data.href){
				window.open(node.data.href, (orgEvent.ctrlKey || orgEvent.metaKey) ? "_blank" : node.data.target);
			}
			// When an external link was clicked, we don't want the node to become
			// active. Also the URL fragment should not be changed
			if( node.data.target === "_blank") {
				return false;
			}
			// Append #HREF to URL without actually loading content
			// (We check for this value on page load re-activate the node.)
			if( window.parent &&  parent.history && parent.history.pushState ) {
				parent.history.pushState({title: node.title}, "", "#" + (node.data.href || ""));
			}
		},
		click: function(event, data){
			// We implement this in the `click` event, because `activate` is not
			// triggered if the node already was active.
			// We want to allow re-loads by clicking again.
			var node = data.node,
				orgEvent = data.originalEvent;

			// Open href (force new window if Ctrl is pressed)
			if(node.isActive() && node.data.href){
				window.open(node.data.href, (orgEvent.ctrlKey || orgEvent.metaKey) ? "_blank" : node.data.target);
			}
		}
	});
	// On page load, activate node if node.data.href matches the url#href
	var tree = $.ui.fancytree.getTree(),
		frameHash = window.parent && window.parent.location.hash;

	if( frameHash ) {
		frameHash = frameHash.replace("#", "");
		tree.visit(function(n) {
			if( n.data.href && n.data.href === frameHash ) {
				n.setActive();
				return false; // done: break traversal
			}
		});
	}
});

</script>

</head>

<body>
	<div id="tree">
	<ul>
	<li class="folder expanded">Documentation
		<ul>
			<li class="external">
				<a target="_blank" href="https://github.com/mar10/fancytree/">Project home</a>
			</li>
			<li class="external">
				<a target="_blank" href="https://github.com/mar10/fancytree/wiki/">Documentation</a>
			</li>
			<li class="external">
				<a target="_blank" href="../doc/jsdoc/">API reference</a>
			</li>
		</ul>
	<li class="folder expanded"> Examples
		<ul>
			<li><a target="content" href="welcome.html">Welcome</a></li>
			<li><a target="content" href="sample-default.html">Default Options</a></li>
			<li><a target="content" href="sample-playground.html">Playground</a></li>
			<li><a target="content" href="sample-configurator.html">Option Configurator</a></li>
			<li><a target="content" href="sample-multi-ext.html">Complex Demo</a></li>
			<li><a target="content" href="sample-ext-grid.html">Grid/Viewport</a></li>
			<li><a target="content" href="sample-source.html">Initialization</a></li>
			<li><a target="content" href="sample-events.html">Event Handling</a></li>
			<li><a target="content" href="sample-api.html">Programming API</a></li>
			<li><a target="content" href="sample-select.html">Checkbox &amp; Select</a></li>
			<li class="external">
				<a target="_blank" href="sample-aria.html">WAI-ARIA</a>
			</li>
			<li class="folder">Glyph Themes
				<ul>
					<li><a target="content" href="sample-ext-glyph-awesome4.html">Font Awesome 4</a></li>
					<li><a target="content" href="sample-ext-glyph-awesome5.html">Font Awesome 5</a></li>
					<li><a target="content" href="sample-ext-glyph-bootstrap3.html">Bootstrap</a></li>
					<li><a target="content" href="sample-ext-glyph-material.html">Material</a></li>
					<li><a target="content" href="sample-ext-glyph-svg.html">SVG</a></li>
				</ul>
			</li>
			<li><a target="content" href="sample-theming.html">Custom Theming</a></li>
			<li><a target="content" href="sample-form.html">Embed in Forms</a></li>
			<li class="external">
				<a target="_blank" href="https://wwWendt.de/tech/fancytree/demo/taxonomy-browser/">
					Taxonomy Browser</a>
			</li>
			<li class="folder">Extensions
				<ul>
					<li><a target="content" href="sample-ext-childcounter.html">Child Counter</a></li>
					<li><a target="content" href="sample-ext-clones.html">Clones</a></li>
					<li><a target="content" href="sample-ext-columnview.html">Column View</a></li>
					<li><a target="content" href="sample-ext-menu.html">Context Menu</a></li>
					<li><a target="content" href="sample-ext-dnd5.html">Drag'n'Drop</a></li>
					<li><a target="content" href="sample-ext-dnd.html">Drag'n'Drop (jQuery UI)</a></li>
					<li><a target="content" href="sample-ext-edit.html">Edit</a></li>
					<li><a target="content" href="sample-ext-filter.html">Filter</a></li>
					<li><a target="content" href="sample-ext-fixed.html">Fixed Headers</a></li>
					<li><a target="content" href="sample-ext-glyph-bootstrap3.html">Glyph Fonts</a></li>
					<li><a target="content" href="sample-ext-grid.html">Grid/Viewport</a></li>
					<li><a target="content" href="sample-ext-logger.html">Logger</a></li>
					<li><a target="content" href="sample-ext-multi.html">Multi-Select</a></li>
					<li class="external"><a target="_blank" href="sample-ext-persist.html">Persist</a></li>
					<li><a target="content" href="sample-ext-table.html">Table</a></li>
					<li><a target="content" href="sample-ext-themeroller.html">ThemeRoller</a></li>
					<li><a target="content" href="sample-ext-wide.html">Wide</a></li>
				</ul>
			</li>
			<li class="folder">Tweaks
				<ul>
					<li><a target="content" href="sample-iframe.html">URL Navigation and &lt;iframe></a></li>
					<li><a target="content" href="sample-accordion.html">Accordion</a></li>
					<li><a target="content" href="sample-load-errors.html">Lazy Load Error Handling</a></li>
					<li><a target="content" href="sample-rtl.html">RTL</a></li>
					<li><a target="content" href="sample-types.html">Node Types</a></li>
					<li><a target="content" href="sample-multiline.html">Large nodes</a></li>
					<li><a target="content" href="sample-multi-dnd5.html">Multiple D'n'd</a></li>
					<!-- <li><a target="content" href="sample-multi-dnd.html">Multiple D'n'd (jQuery UI)</a></li> -->
					<li><a target="content" href="sample-scroll.html">Smart Scrolling</a></li>
					<li><a target="content" href="sample-webservice.html">Webservice</a></li>
					<li><a target="content" href="../test/test-ext-keyboard.html">Keyboard Nav.</a></li>
				</ul>
			</li>
			<li class="folder">Test
				<ul>
					<li class="external"><a target="_blank" href="../test/unit/test-core.html">Core Unit Tests</a></li>
					<li class="external"><a target="_blank" href="../test/unit/test-suite.html">Suite</a></li>
					<li><a target="content" href="../test/test-ext-dnd.html">Drag'n'Drop</a></li>
					<li class="external"><a target="_blank" href="../test/unit/test-bench.html">Benchmarks</a></li>
<!--
					<li><a target="content" href="sample-pyserver.html">Local server</a>
					<li class="folder">DTD
						<ul>
							<li><a target="content" href="../test/doctypes/doctype-none.html">No DTD</a></li>
							<li><a target="content" href="../test/doctypes/doctype-html4-loose.html">HTML4 transitional</a></li>
							<li><a target="content" href="../test/doctypes/doctype-html4-strict.html">HTML4 strict</a></li>
							<li><a target="content" href="../test/doctypes/doctype-html5.html">HTML5</a></li>
							<li><a target="content" href="../test/doctypes/doctype-xml-transitional.html">XHTML transitional</a></li>
							<li><a target="content" href="../test/doctypes/doctype-xml-strict.html">XHTML strict</a></li>
						</ul>
					</li>
-->
				</ul>
			</li>
		</ul>
		<li class="folder expanded">3rd Party
			<ul>
				<li class="folder">Extensions
					<ul>
						<li><a target="content" href="../3rd-party/extensions/contextmenu/contextmenu.html">jQuery contextMenu Extension</a></li>
						<li><a target="content" href="../3rd-party/extensions/hotkeys/hotkeys.html">hotkeys</a></li>
					</ul>
				</li>
				<li class="folder">Samples
					<ul>
						<li><a target="content" href="sample-3rd-confirm.html">jquery-confirm</a></li>
						<li><a target="content" href="sample-3rd-jQuery-contextMenu.html">jQuery contextMenu</a></li>
						<li><a target="content" href="sample-3rd-ui-contextmenu.html">ui-contextmenu</a></li>
						<li><a target="content" href="sample-3rd-contextmenu-abs.html">Context menu (ABS), Copy/paste</a></li>
						<li><a target="content" href="sample-3rd-grid-scrollbar.html">Scrollbar for ext-grid</a></li>
					</ul>
				</li>
			</ul>
		</li>
	</ul>
	</div>
</body>
</html>
