<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Multi-Select</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.multi.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">
</style>

<script type="text/javascript">
$(function(){
	$("#tree").fancytree({
		extensions: ["multi"],
		source: {url: "ajax-tree-products.json"},
		checkbox: true,
		unselectable: function(event, data) {
			return data.node.isFolder();
		},
		multi: {
			mode: "sameParent",
		},
		activate: function(event, data) {
			$("#lblActive").text("" + data.node);
		},
		lazyLoad: function(event, data) {
			data.result = { url: "ajax-sub2.json", debugDelay: 1000 };
		},
		init: function(event, data) {
		},
	});
});
</script>

<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	addSampleButton({
		label: "Visit down",
		newline: false,
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			console.log(node)
			node.tree.visitRows(function(n){
				n.debug();
			}, {includeHidden: false, start: node, includeSelf: true});

		}
	});
	$("#cbCheckboxes").on("change", function(){
		$.ui.fancytree.getTree( "#tree" ).setOption("checkbox", $(this).is(":checked"));
	});
	$("#cbCheckboxAutoHide").on("change", function(){
		$.ui.fancytree.getTree( "#tree" ).setOption("checkboxAutoHide", $(this).is(":checked"));
	});
});
</script>
<!-- End_Exclude -->

</head>

<body class="example">
	<h1>Example: 'multi' extension</h1>
	<div class="description">
		<p>
			Select node ranges using mouse and keyboard and
			<kbd>Ctrl</kbd>, <kbd>Command</kbd>, <kbd>Shift</kbd> modifiers.
		</p>
		<p>
			Edit the node titles with `[Shift] + click` '[Shift] + [Down]', ....
		</p>
		<p>
			<b>Status:</b> experimental.
			<b>Details:</b>
			<a href="https://github.com/mar10/fancytree/wiki/ExtMulti"
				target="_blank" class="external">ext-multi</a>.
		</p>
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<div>
		<label for="lbMode">Mode:</label>
		<select id="lbMode">
			<option value="">Unrestricted</option>
			<option value="sameParent">Same parent</option>
			<option value="sameLevel">Same level</option>
		</select>
		<input id="cbCheckboxes" type="checkbox" checked>
		<label for="cbCheckboxes">Show checkboxes</label>
		<input id="cbCheckboxAutoHide" type="checkbox">
		<label for="cbCheckboxAutoHide">Auto-hide checkboxes</label>
	</div>

	<div id="tree">
	</div>
	<p>Active: <span id="lblActive">-</span></p>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
