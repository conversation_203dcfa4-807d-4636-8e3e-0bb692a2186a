<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Events</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<script type="text/javascript">
	$.ui.fancytree.debugLevel = 3; // silence debug output

	function logEvent(event, data, msg){
//        var args = $.isArray(args) ? args.join(", ") :
		msg = msg ? ": " + msg : "";
		$.ui.fancytree.info("Event('" + event.type + "', node=" + data.node + ")" + msg);
	}
	$(function(){
		$("#tree").fancytree({
			checkbox: true,
			// --- Tree events -------------------------------------------------
			blurTree: function(event, data) {
				logEvent(event, data);
			},
			create: function(event, data) {
				logEvent(event, data);
			},
			init: function(event, data, flag) {
				logEvent(event, data, "flag=" + flag);
			},
			focusTree: function(event, data) {
				logEvent(event, data);
			},
			restore: function(event, data) {
				logEvent(event, data);
			},
			// --- Node events -------------------------------------------------
			activate: function(event, data) {
				logEvent(event, data);
				var node = data.node;
				// acces node attributes
				$("#echoActive").text(node.title);
				if( !$.isEmptyObject(node.data) ){
//					alert("custom node data: " + JSON.stringify(node.data));
				}
			},
			beforeActivate: function(event, data) {
				logEvent(event, data, "current state=" + data.node.isActive());
				// return false to prevent default behavior (i.e. activation)
//              return false;
			},
			beforeExpand: function(event, data) {
				logEvent(event, data, "current state=" + data.node.isExpanded());
				// return false to prevent default behavior (i.e. expanding or collapsing)
//				return false;
			},
			beforeSelect: function(event, data) {
//				console.log("select", event.originalEvent);
				logEvent(event, data, "current state=" + data.node.isSelected());
				// return false to prevent default behavior (i.e. selecting or deselecting)
//				if( data.node.isFolder() ){
//					return false;
//				}
			},
			blur: function(event, data) {
				logEvent(event, data);
				$("#echoFocused").text("-");
			},
			click: function(event, data) {
				logEvent(event, data, ", targetType=" + data.targetType);
				// return false to prevent default behavior (i.e. activation, ...)
				//return false;
			},
			collapse: function(event, data) {
				logEvent(event, data);
			},
			createNode: function(event, data) {
				// Optionally tweak data.node.span or bind handlers here
				logEvent(event, data);
			},
			dblclick: function(event, data) {
				logEvent(event, data);
//				data.node.toggleSelect();
			},
			deactivate: function(event, data) {
				logEvent(event, data);
				$("#echoActive").text("-");
			},
			expand: function(event, data) {
				logEvent(event, data);
			},
			enhanceTitle: function(event, data) {
				logEvent(event, data);
			},
			focus: function(event, data) {
				logEvent(event, data);
				$("#echoFocused").text(data.node.title);
			},
			keydown: function(event, data) {
				logEvent(event, data);
				switch( event.which ) {
				case 32: // [space]
					data.node.toggleSelected();
					return false;
				}
			},
			keypress: function(event, data) {
				// currently unused
				logEvent(event, data);
			},
			lazyLoad: function(event, data) {
				logEvent(event, data);
				// return children or any other node source
				data.result = {url: "ajax-sub2.json"};
//				data.result = [
//					{title: "A Lazy node", lazy: true},
//					{title: "Another node", selected: true}
//					];
			},
			loadChildren: function(event, data) {
				logEvent(event, data);
			},
			loadError: function(event, data) {
				logEvent(event, data);
			},
			modifyChild: function(event, data) {
				logEvent(event, data, "operation=" + data.operation +
					", child=" + data.childNode);
			},
			postProcess: function(event, data) {
				logEvent(event, data);
				// either modify the Ajax response directly
				data.response[0].title += " - hello from postProcess";
				// or setup and return a new response object
//				data.result = [{title: "set by postProcess"}];
			},
			renderNode: function(event, data) {
				// Optionally tweak data.node.span
//              $(data.node.span).text(">>" + data.node.title);
				logEvent(event, data);
			},
			renderTitle: function(event, data) {
				// NOTE: may be removed!
				// When defined, must return a HTML string for the node title
				logEvent(event, data);
//				return "new title";
			},
			select: function(event, data) {
				logEvent(event, data, "current state=" + data.node.isSelected());
				var s = data.tree.getSelectedNodes().join(", ");
				$("#echoSelected").text(s);
			}
		}).on("fancytreeactivate", function(event, data){
			// alternative way to bind to 'activate' event
//		    logEvent(event, data);
		}).on("mouseenter mouseleave", ".fancytree-title", function(event){
			// Add a hover handler to all node titles (using event delegation)
			var node = $.ui.fancytree.getNode(event);
			node.info(event.type);
		});
	});
</script>
<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	addSampleButton({
		label: "(De)Select active node",
		newline: false,
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			node.setSelected( !node.isSelected() );
		}
	});
	addSampleButton({
		label: "Remove active node",
		newline: false,
		code: function(){
			var node = $.ui.fancytree.getTree("#tree").getActiveNode();
			node.remove();
		}
	});
});
</script>
<!-- End_Exclude -->
</head>

<body class="example">
	<h1>Example: Events</h1>
	<div class="description">
		Implements all callbacks: Open your browser's console to see the event log.
		<br>
		The 'links' folders contain nodes with a custom data.url option.
		This is used to open the URL in the onActivate event.
		<br>
		See the <a href="https://github.com/mar10/fancytree/wiki/TutorialEvents"
			target="_blank" class="external">Events Tutorial</a>
		for details.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>

	<p id="sampleButtons">
	</p>

	<div id="tree">
		<ul>
			<li class="folder">jQuery links
				<ul>
					<li data-foo="bar">jQuery home
					<li data-json='{"url": "http://docs.jquery.com"}'>jQuery docs
				</ul>

			<li class="folder">Other links
				<ul>
					<li data-url="http://code.google.com">Google Code
				</ul>

			<li class="folder">Lazy loading
				<ul>
					<li id="k123" class="lazy">This is a lazy loading document with key k123.
					<li id="k234" class="lazy folder">This is a lazy loading folder with key k234.
				</ul>
		</ul>
	</div>

	<div>Active node: <span id="echoActive">-</span></div>
	<div>Selected node list: <span id="echoSelected">-</span></div>
	<div>Focused node: <span id="echoFocused">-</span></div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
