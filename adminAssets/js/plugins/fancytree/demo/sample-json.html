<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

	<style type="text/css">
/*	span.fancytree-node span.fancytree-icon {
		background-image: url("skin-custom/bug.png");
		background-position: 0 0;
	}*/
	span.fancytree-node.no span.fancytree-icon {
		background-position: 0 0;
		background-image: url("skin-custom/exclamation.png");
	}
	span.fancytree-node.yes span.fancytree-icon {
		background-position: 0 0;
		background-image: url("skin-custom/accept.png");
	}
	span.fancytree-node.link span.fancytree-icon {
		background-position: 0 0;
		background-image: url("skin-custom/arrow_right.png");
	}
	span.fancytree-node.answer span.fancytree-icon {
		background-position: 0 0;
		background-image: url("skin-custom/emoticon_smile.png");
	}
	</style>
	<script type="text/javascript">
		$(function(){
			// using default options
			$("#tree").fancytree({
				source: {url: "ajax-tree-decide.json"}
			});
		});
	</script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<script type="text/javascript">
		$(function(){
			addSampleButton({
				label: "Products",
				code: function(){
					$.ui.fancytree.getTree("#tree").reload({url: "ajax-tree-products.json"});
				}
			});
			addSampleButton({
				label: "Files",
				code: function(){
					$.ui.fancytree.getTree("#tree").reload({url: "ajax-tree-fs.json"});
				}
			});
		});
	</script>
	<!-- End_Exclude -->
</head>

<body class="example">
	<h1>Example: Default</h1>
	<div class="description">
		This tree uses default options.<br>
		It is initialized from a hidden &lt;ul> element on this page.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<div id="tree"></div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p id="sampleButtons">
	</p>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
