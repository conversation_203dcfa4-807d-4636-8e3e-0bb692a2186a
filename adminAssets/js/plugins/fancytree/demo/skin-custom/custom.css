/*******************************************************************************
 * Sample for a Dynatree custom stylesheet.
 * This is expected to be included after the standard ui.dynatree.css, thus
 * overridung the defaults.
 */
.dynatree-has-children span.dynatree-icon
{
	background-position: 0 0;
	background-image: url("doc_with_children.gif");
}

.dynatree-ico-cf span.dynatree-icon  /* Collapsed Folder */
{
	background-image: url("folder_docs.gif");
}

.dynatree-ico-ef span.dynatree-icon  /* Expanded Folder */
{
	background-image: url("folder_images.gif");
}


/*******************************************************************************
 * Node titles
 */

span.dynatree-has-children a
{
	font-style: oblique;
}

span.dynatree-selected a
{
	color: green;
	font-style: italic;
}

span.dynatree-active a,
span.dynatree-active a:hover
{
	border: 1px solid maroon;
	background-color: #FAD8F0 !important; /* reddish */
}


/*******************************************************************************
 * Custom node classes (sample)
 */

span.custom1 a
{
	background-color: #ffffbb;
	color: maroon;
}
span.custom1 span.dynatree-icon
{
	background-position: 0 0;
	background-image: url("customDoc2.gif");
}

span.custom2 a
{
	font-weight: bold;
	background-color: silver;
	color: navy;
}
span.custom2 span.dynatree-icon
{
	background-position: 0 0;
	background-image: url("folder_page.gif");
}
