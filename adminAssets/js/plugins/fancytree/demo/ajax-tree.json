[{"title": "simple node (no explicit id, so a default key is generated)"}, {"key": "2", "title": "item1 with key and tooltip", "tooltip": "Look, a tool tip!"}, {"key": "3", "title": "<span>item2 with <b>html</b> inside a span tag</span>"}, {"key": "4", "title": "this nodes uses 'nolink', so no &lt;a> tag is generated", "nolink": true}, {"key": "5", "title": "using href", "href": "https://wwWendt.de/"}, {"key": "6", "title": "node with some extra classes (will be added to the generated markup)", "extraClasses": "my-extra-class"}, {"key": "10", "title": "Folder 1", "folder": true, "children": [{"key": "10_1", "title": "Sub-item 1.1", "children": [{"key": "10_1_1", "title": "Sub-item 1.1.1"}, {"key": "10_1_2", "title": "Sub-item 1.1.2"}]}, {"key": "10_2", "title": "Sub-item 1.2", "children": [{"key": "10_2_1", "title": "Sub-item 1.2.1"}, {"key": "10_2_2", "title": "Sub-item 1.2.2"}]}]}, {"key": "20", "title": "Simple node with active children (expand)", "expanded": true, "children": [{"key": "20_1", "title": "Sub-item 2.1", "children": [{"key": "20_1_1", "title": "Sub-item 2.1.1"}, {"key": "20_1_2", "title": "Sub-item 2.1.2"}]}, {"key": "20_2", "title": "Sub-item 2.2", "children": [{"key": "20_2_1", "title": "Sub-item 2.2.1"}, {"key": "20_2_2", "title": "Sub-item 2.2.2"}]}]}, {"key": "30", "title": "Lazy folder", "folder": true, "lazy": true}, {"key": "31", "title": "Lazy folder (preload)", "folder": true, "lazy": true, "preload": true}, {"key": "32", "title": "Lazy folder (expand on load)", "folder": true, "lazy": true, "expanded": true}]