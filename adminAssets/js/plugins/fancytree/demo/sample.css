a.external {
	background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAMAAAC67D+PAAAAFVBMVEVmmcwzmcyZzP8AZswAZv////////9E6giVAAAAB3RSTlP///////8AGksDRgAAADhJREFUGFcly0ESAEAEA0Ei6/9P3sEcVB8kmrwFyni0bOeyyDpy9JTLEaOhQq7Ongf5FeMhHS/4AVnsAZubxDVmAAAAAElFTkSuQmCC") 100% 50% no-repeat;
	padding-right: 13px;
}
body.example {
	margin: 15px;
	font-family: sans-serif;
	font-size: 1em;
  color: #212529;
}
body.example h1 {
	font-size: 1.2em;
}
body.example h2 {
	font-size: 1em;
}

body.example .description,
body.example .sample-links {
  background-color: #f7f7f7;
	/* background-color: #d0d0f0; */
	padding: 5px;
	margin: 5px 0;
	font-size: small;
}

body.example fieldset {
  border: 1px solid gray;
  padding: 4px;
  border-radius: 2px;
}
body.example fieldset legend {
  padding-left: 6px;
  padding-right: 6px;
}

p.version-info {
	color: gray;
	font-size: 0.6em;
}

p.warning,
p.info,
div.info {
	font-size: small;
	background-color: #fff3cd;
	background-image: url(../doc/iconInfo_32x32.png);
	background-repeat: no-repeat;
	padding: 5px;
	padding-left: 40px;
	min-height: 25px;
}
.sampleButtonContainer
{
	margin-right: 10px;
}
.sampleButtonContainer a
{
	color: #212529;
	text-decoration: undereline;
	text-decoration-style: dotted;
	padding: 1px 3px;
	font-size: 70%;
}
.sampleButtonContainer button
{
	margin-bottom: 3px;
}
p#sampleButtons h5
{
	font-size: 9pt;
	margin-top: 3px;
	margin-bottom: 3px;
	font-weight: bold;
}

.description a,
.description a:hover,
.description a:visited,
p.sample-links a,
p.sample-links a:hover,
p.sample-links a:visited
{
	color: #212529;
  /* text-decoration: none; */
  text-decoration-style: dotted;
  /* font-weight: 600; */
}
.description a:hover,
p.sample-links a:hover
{
	text-decoration: underline;
}
p.sample-links a,
p.sample-links a:hover,
p.sample-links a:visited
{
	margin-left: 15px;
	padding: 1px 3px;
	font-size: small;
}

pre{
  background:#f7f7f7;
  border:1px solid #999;
  padding:.5em;
  margin:.5em;
  font-size:.9em;
}

body.example button {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
	transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
	/* small: */
	padding: .15rem .3rem;
	font-size: .675rem;
	line-height: 1.2;
	border-radius: .2rem;
	/* .btn-secondary */
	color: #fff;
	background-color: #6c757d;
	border-color: #6c757d;
}
[type=button]:not(:disabled), [type=reset]:not(:disabled), [type=submit]:not(:disabled), button:not(:disabled) {
  cursor: pointer;
}

body.example button:hover {
	text-decoration: none;
	/* .btn-secondary */
	background-color: #5a6268;
	border-color: #545b62;
}

body.example button:focus {
    box-shadow: 0 0 0 0.2rem rgba(130,138,145,.5);
}

body.example input[type=submit],
body.example button[type=submit] {
	/* .btn-primary */
	color: #fff;
	background-color: #007bff;
	border-color: #007bff;
}
body.example input[type=submit]:hover,
body.example button[type=submit]:hover {
	/* .btn-primary */
  background-color: #0069d9;
  border-color: #0062cc;
}



ul.fancytree-container {
	margin: 4px; /* leave some room for the safari focus border */
}

iframe.embedded-plunkr {
	border: 1px solid #f7f7f7;
	width: 100%;
	height: 500px;
}
iframe.embedded-jsbin {
	border: 1px solid #f7f7f7;
	width: 100%;
	height: 500px;
}
