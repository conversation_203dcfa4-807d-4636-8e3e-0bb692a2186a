<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Menu</title>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
</script>
</head>
<body class="example">
	<h1>Example: 'menu' extension</h1>
	<div class="description">
		<p>
			Fancytree does <b>not</b> include a native context menu extension.<br>
			A context menu is typically used for multiple elements of a web
			application and the appearance should be consistent everywhere.
		</p><p>
			However, using an <i>existing</i> context menu plugin is easy.
			It normally only requires to call <code>$.ui.fancytree.getNode(element)</code>
			in the respective callback handler and then trigger the desired
			operation.
		</p><p>
			Examples of existing third party context menus include:
			<ul>
			<li><b>jQuery contextMenu</b><br>
				A popular, free context menu and polyfil
				(<a href="https://github.com/swisnl/jQuery-contextMenu" target="_blank" class="external">project site</a>).<br>
				See here for a <a href="index.html#sample-3rd-jQuery-contextMenu.html" target="_top">Fancytree demo</a>.

			<li><b>jQuery contextMenu (using a Fancytree extension)</b><br>
				The <code>ext-contextMenu</code> extension is a thin wrapper that
				integrates the above plugin into Fancytree (
				<a href="index.html#../3rd-party/extensions/contextmenu/contextmenu.html" target="_top">demo</a> ).

			<li><b>jquery.ui-contextmenu</b><br>
				A free jQuery plugin that turns a <a href="http://jqueryui.com/menu/" target="_blank" class="external">jQuery UI Menu</a>
				widget into a context menu
				(<a href="https://github.com/mar10/jquery-ui-contextmenu" target="_blank" class="external">project site</a>).<br>
				See here for a <a href="index.html#sample-3rd-ui-contextmenu.html" target="_top">Fancytree demo</a>.

			<li><b>Plenty of other menus &hellip;</b><br>
				<a href="https://github.com/tweego/JeeGooContext" target="_blank" class="external">JeeGooContext</a> and
				<a href="https://www.npmjs.com/search?q=contextmenu" target="_blank" class="external">more</a>&hellip;
			</ul>
		</p>
	</div>

</body>
</html>
