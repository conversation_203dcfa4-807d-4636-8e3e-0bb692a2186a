<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - WAI-ARIA Tree Grid</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-lion/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.table.js"></script>
	<script src="../src/jquery.fancytree.ariagrid.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

	<style type="text/css">
		td.stretch input,
		td.stretch select {
			box-sizing: border-box;
			margin: 2px;
			width: 100%;
		}
		td.center {
			text-align: center;
		}
		table.fancytree-container > thead > tr > th {
			background-color: #d4d4d4;
		}
		/* --- ext-ariagrid specific ---------------------------------------- */
		/* table.fancytree-cell-mode > tbody > tr.fancytree-active > td {
			background-color: #eee;
		}
		table.fancytree-cell-mode > tbody > tr > td.fancytree-active-cell {
			background-color: #cbe8f6;
		}
		table.fancytree-cell-mode.fancytree-cell-nav-mode > tbody > tr > td.fancytree-active-cell {
			background-color: #3875d7;
		} */
		/* --- Debug helpers: ----------------------------------------------- */
/*
		[tabindex="-1"] {
			border: 2px dotted green !important;
		}
		[tabindex="0"] {
			border: 2px dotted red !important;
		}
*/
	</style>

<!-- Start_Exclude: This block is not part of the sample code -->
<script>
	$(function () {
		addSampleButton({
			label: "Activate cell 4/3",
			newline: false,
			code: function () {
				var node = $.ui.fancytree.getTree("#treegrid").getNodeByKey("_4");
				node.setActive(true, {cell: 3});
			}
		});
	});
</script>
<!-- End_Exclude -->

	<script type="text/javascript">
		/*global $ */
		$( function() {
			// --- Demo GUI: ---------------------------------------------------

			$( "#optionsForm [name=cellFocus]" ).change( function( e ) {
				var value = $( this ).find( ":selected" ).val();

				window.sessionStorage.setItem( "cellFocus", value );
				$.ui.fancytree.getTree( "#treegrid" ).setOption("ariagrid.cellFocus", value);
			}).val( window.sessionStorage.getItem( "cellFocus" ) || "allow" );

			// --- Fancytree widget --------------------------------------------

			$( "#treegrid" ).fancytree({
				extensions: [ "table", "ariagrid" ],
				generateIds: true,
				// rtl: true,
				checkbox: true,
//				quicksearch: true,
				source: { url: "ajax-tree-products.json" },
				table: {
					checkboxColumnIdx: 0,  // render the checkboxes into the 1st column
					nodeColumnIdx: 1       // render the node title into the 2nd column
				},
				ariagrid: {
					cellFocus: $( "#optionsForm [name=cellFocus]" ).find( ":selected" ).val(),
					label: "Fancytree ARIA sample"
				},
				init: function( event, data ) {
					// We could switch to cell-mode here:
					// data.tree.getFirstChild().setActive(true, {cell: 1});
				},
				lazyLoad: function(event, data) {
					data.result = {url: "ajax-sub2.json"}
				},
				activate: function(event, data) {
					data.node.debug(event.type, data);
				},
				activateCell: function(event, data) {
					data.node.debug(event.type, data);
				},
				defaultGridAction: function( event, data ) {
					// Called when ENTER is pressed in cell-mode.
					// Return false to prevent default
					data.node.debug(event.type, data);
					if( !data.activeTd ) {
						alert( "Custom default action for row: " + data.node.title );
						// we don't return false, so default action is applied:
					} else if ( data.colIdx === 4 ) {
						// eslint-disable-next-line no-alert
						alert( "Custom default action: " + data.node.title );
						return false;
					}
				},
				renderColumns: function( event, data ) {
					var node = data.node,
						$tdList = $( node.tr ).find( ">td" );

					// The row template inserts controls, so we have to remove
					// them here for folders:
					if ( node.isFolder() ) {
						$tdList.eq( 2 ).empty();
						$tdList.eq( 3 ).empty();
						$tdList.eq( 5 ).empty();
						$tdList.eq( 6 ).empty();
						$tdList.eq( 7 ).empty();
					}
					$tdList.eq( 4 ).text( "Lorem ipsum " + node.title );
				}
			});

		});
</script>
</head>

<body class="example">
	<h1>Example: WAI-ARIA Tree Grid - Prototype</h1>
	<div class="description">
		<p>
			This Fancytree Tree Grid has uses a prototypical extension to
			explore ARIA support as discussed here:

			<a href="https://rawgit.com/w3c/aria-practices/treegrid/examples/treegrid/treegrid-1.html">
				w3c/aria-practices/treegrid/examples/treegrid/treegrid-1.html
			</a>

			See also the <a href="https://github.com/mar10/fancytree/wiki/ExtTable#ext-ariagrid">Documentation</a>.

			See also
			<a href="https://github.com/w3c/aria-practices/issues/91">here</a>
			and
			<a href="https://rawgit.com/jongund/aria-practices/treegrid/examples/treegrid/treegrid-1.html">here</a>

<!--
		</p><p>
			<strong>Note:</strong> please
			<a href="https://github.com/mar10/fancytree/issues/655">provide feedback</a>
			if you have suggestions for improvement on the Fancytree implementation.
-->
		</p> <p>
			See also the <a href="sample-aria.html">ARIA Tree View example</a>.
		</p>
	</div>

	<div>
		<form id="optionsForm">
			<label>cellFocus:
				<select name="cellFocus">
					<option value="start">start</option>
					<option value="allow" selected="selected">allow</option>
					<option value="force">force</option>
					<option value="off">off</option>
				</select>
			</label>
			(Please reload page for changes to take effect.)
		</form>
		<hr>

		<table id="treegrid">
			<colgroup>
			<col width="20px">
			<col width="400px">
			<col width="30px">
			<col width="30px">
			<col width="400px">
			<col width="100px">
			<col width="100px">
			<col width="100px">
			</colgroup>
			<thead>
				<tr> <th>Select</th> <th>Item</th> <th>In Stock</th> <th>Favorite</th> <th>Details</th> <th>Qty</th> <th>Link</th> <th>Drop</th> </tr>
			</thead>
			<tbody>
				<tr>
					<td class="center">></td>
					<td></td>
					<td class="center"><input type="checkbox" tabindex="-1"></td>
					<td class="center"><input type="checkbox" tabindex="-1"></td>
					<td class="stretch"><input type="text" tabindex="-1"></td>
					<td class="stretch"><input type="number" min="0" value="0" tabindex="-1"></td>
					<td class="center"><a href='http://google.com' target='_blank' tabindex='-1'>link</a></td>
					<td class="stretch"><select tabindex="-1"><option>a</option><option>b</option></select></td>
				</tr>
			</tbody>
		</table>

		<label for="lblAfter">Text 2</label><input type="text" id="lblAfter">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
