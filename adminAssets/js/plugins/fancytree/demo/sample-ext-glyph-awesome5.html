<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<title>Fancytree - Example: Font Awesome 5</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="https://use.fontawesome.com/releases/v5.0.13/css/all.css" rel="stylesheet">

	<link href="../src/skin-awesome/ui.fancytree.css" rel="stylesheet" class="skinswitcher">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.dnd5.js"></script>
	<script src="../src/jquery.fancytree.glyph.js"></script>

<!-- Start_Exclude: This block is not part of the sample code -->
	<!-- Used for font slider: -->
	<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="../demo/sample.css" rel="stylesheet">
	<script src="../demo/sample.js"></script>

<style type="text/css">
</style>
<!-- End_Exclude -->


	<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		// Initialize Fancytree
		$("#tree").fancytree({
			extensions: ["dnd5", "glyph"],
			checkbox: true,
			selectMode: 3,
      dnd5: {
        autoExpandMS: 1500,
        dragStart: function(node, data) {
          return true;
        },
        dragEnter: function(node, data) {
          return true;
        },
      },
			glyph: {
				preset: "awesome5",
				map: {}
			},
			source: {url: "ajax-tree-products.json", debugDelay: 1000},
			// source: {url: "ajax-tree-taxonomy.json", debugDelay: 1000},
			lazyLoad: function(event, ctx) {
				ctx.result = {url: "ajax-sub2.json", debugDelay: 2000};
			}
		});
	});
</script>

<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	// Adjust skinswitcher for current folder layout
	function setSize(event, ui){
		$("#tree .fancytree-container").css("font-size", ui.value + "pt");
		$("#curSize").text(ui.value + " pt");
	}
	$( "#fontSlider" ).slider({
		min: 4,
		max: 64,
		value: 10,
		change: setSize,
		slide: setSize
	 }).slider("value", 10);
});
</script>
<!-- End_Exclude -->

</head>

<body class="example">
	<h1>Example: 'glyph' extension with 'font-awesome' theme and Font Awesome 5 Web Font/CSS icons</h1>
	<div class="description">
		<p>
			The 'glyph' extension adds <code>fa-...</code> classes to the
			node's <code>span</code> tags, so scalable vector icons as provided by
			<a href="http://fontawesome.io/" class="external" target="_blank">Font Awesome</a>
			can be used.<br>
			See also
			<a href="https://github.com/mar10/fancytree/wiki/ExtGlyph"
				class="external" target="_blank">ext-glyph</a>.
		</p>
	</div>

	<hr>
	Font size: <span id="curSize"></span>
	<div id="fontSlider"></div>
	<hr>
	<div id="tree">
	</div>

<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
<!-- End_Exclude -->
</body>
</html>
