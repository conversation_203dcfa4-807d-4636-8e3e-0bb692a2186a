<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Theming</title>

	<link href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">
	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<!-- Include the basic stylesheet: -->
	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<!-- Add som custom fonts -->
	<link href="//fonts.googleapis.com/css?family=Audiowide" rel="stylesheet">
	<!-- Override CSS with a custom stylesheet : -->
	<link href="skin-custom/custom.css" rel="stylesheet" >
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">
span.fancytree-node.custom1 > span.fancytree-title {
	color: maroon;
	font-family: "Audiowide";
}
span.fancytree-node.custom1 > span.fancytree-icon {
	background-position: 0 0;
	background-image: url("skin-custom/customDoc2.gif");
}
</style>

<script type="text/javascript">
	$(function(){
		$("#tree").fancytree({
			// Image folder used for data.icon attribute.
			imagePath: "skin-custom/",
			// icon: false,
			renderNode: function(event, data) {
				// Optionally tweak data.node.span
				var node = data.node;
				if(node.data.cstrender){
					var $span = $(node.span);
					$span.find("> span.fancytree-title").text(">> " + node.title).css({
						fontStyle: "italic"
					});
					$span.find("> span.fancytree-icon").css({
//                      border: "1px solid green",
						backgroundImage: "url(skin-custom/customDoc2.gif)",
						backgroundPosition: "0 0"
					});
				}
			}
		});
	});
</script>
<!-- Start_Exclude: This block is not part of the sample code -->
<script>
	$(function(){
		addSampleButton({
			label: "Set icons",
			code: function(){
				var tree = $.ui.fancytree.getTree("#tree"),
					node = tree.getNodeByKey("node_4.1");
				node.extraClasses = "custom1";
				node.renderTitle();
				node = tree.getNodeByKey("node_4.2");
				node.icon = "customDoc1.gif";
				node.renderTitle();
			}
		});
	});
</script>
<!-- End_Exclude -->
</head>

<body class="example">
	<h1>Example: Theming</h1>
	<p class="description">
		Include a custom CSS <i>after</i> the standard CSS to override theming.<br>
		Some nodes have their <code>data.addClass</code> attribute set.<br>
		Finally, the last two nodes use the <code>data.icon</code> attribute.
		<br>
		See the <a href="https://github.com/mar10/fancytree/wiki/TutorialTheming"
			target="_blank" class="external">Theming Tutorial</a>
		for details.
	</p>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<p id="sampleButtons">
	</p>

	<div id="tree">
		<ul>
			<li class="folder expanded">Standard nodes, modified by extra CSS rules
				<ul>
					<li id="node_4.1">Sub-item 4.1
					<li id="node_4.2">Sub-item 4.2
				</ul>

			<li class="folder expanded">Override CSS style per node
				<ul>
					<li class="custom1">class="custom1": Additional classes are copied over to outer &lt;span>
					<li class="folder custom1">class="custom1": Additional classes are copied over to outer &lt;span>
				</ul>

			<li class="folder expanded">Use 'data-NAME' attributes to define additional data
				<ul>
					<li data-icon="customDoc1.gif">Node with standard CSS, but custom icon
					<li class="folder" data-icon="folder_docs.gif">Folder with standard CSS but custom icon
					<li data-icon="ui-icon ui-icon-heart">'icon' is directly added to the image &lt;span>, so jQuery stock icons may be used
				</ul>

			<li class="folder expanded">Use 'data-json' attribute to define additional data
				<ul>
					<li data-json='{"icon": "customDoc1.gif"}'>Node with standard CSS, but custom 'data-icon'
				</ul>

			<li class="folder expanded">Use render callback
				<ul>
					<li id="renderNode1" data-cstrender="true">Node hat will be handled by 'render' calback
				</ul>

		</ul>
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
