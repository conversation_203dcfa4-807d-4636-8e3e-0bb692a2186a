<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: childcounter</title>

	<link href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">
	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.childcounter.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">
</style>

<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		$("#tree").fancytree({
			extensions: ["childcounter"],
			source: {
				url: "ajax-tree-plain.json"
			},
			childcounter: {
				deep: true,
				hideZeros: true,
				hideExpanded: true
			},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			},
			loadChildren: function(event, data) {
				// update node and parent counters after lazy loading
				data.node.updateCounters();
			}
		});
	});
</script>
</head>

<body class="example">
	<h1>Example: 'childcounter' extension</h1>
	<div class="description">
		<p>
			Display count of children at parent node.
		</p>
		<p>
			The main puropse of this extension is to serve as an example for
			Fanytree's the general extension concept:
			<br>
			See the
			<a class="external" href="../doc/annotated-src/jquery.fancytree.childcounter.html">annotated source</a>
			to learn how to write a Fancytree extension.<br>
			Click the [View source code] link below, to see how an extension is used.
		</p>
		<p>
			<b>Status</b>: beta
		</p>
	</div>

	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<!-- Add a <table> element where the tree should appear: -->
	<div id="tree">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
