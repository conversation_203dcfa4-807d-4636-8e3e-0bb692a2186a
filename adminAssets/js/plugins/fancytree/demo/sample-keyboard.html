<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>
	<script src="../src/jquery-ui-dependencies/jquery.fancytree.ui-deps.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.gridnav.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>

	<style type="text/css">
		.fancytree-node input {
			margin-left: 10px;
		}
	</style>
	<!-- End_Exclude -->

<script type="text/javascript">
	function createNode(e, ctx) {
		// add fake <input/> to the node
		var $input = $("<input/>").val(ctx.node.title);
		$("span.fancytree-title", ctx.node.li).before($input);
	}

	$(function () {
		var source = [
			{
				title: "Item 1",
				children: [
					{ title: "Item 1.1" },
					{ title: "Item 1.2" }
				]
			}, {
				title: "Item 2",
				children: [
					{ title: "Item 2.1" },
					{ title: "Item 2.2" }
				]
			}, {
				title: "Item 3",
				children: [
					{ title: "Item 3.1" },
					{ title: "Item 3.2" }
				]
			}, {
				title: "Item 4",
				children: [
					{ title: "Item 4.1" },
					{ title: "Item 4.2" }
				]
			}
		];

		$("#tree1").fancytree({
			source: source,
			checkbox: true,
			createNode: createNode,
			tabbable: true
		});

		$("#tree2").fancytree({
			source: source,
			checkbox: true,
			createNode: createNode,
			tabbable: false
		});

		$("#tree3").fancytree({
			source: source,
			checkbox: true,
			createNode: createNode,
			tabbable: "nodes"
		});
	});
</script>
</head>

<body class="example">
	<h1>Example: keyboard navigation</h1>
	<!-- Start_Exclude: This block is not part of the sample code -->
	<div class="description">
		This example demonstrates the navigation inside and outside of the tree with TAB and arrow keys.<br/>
		There are three trees with different values of tabbable option.<br/>
		NOTE: The example is experimental: backspace, spacebar and arrow keys don't work correctly in input fields for now.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<hr>
	<!-- End_Exclude -->

	<div>
		<label>Fake input:<input id="input1"/></label>
	</div>
	<br/>

	<div>
		<label>Tree with 'tabbable = true':</label>
		<div id="tree1"></div>
	</div>
	<br/>

	<div>
		<label>Fake input:<input id="input2"/></label>
	</div>
	<br/>

	<div>
		<label>Tree with 'tabbable = false':</label>
		<div id="tree2"></div>
	</div>
	<br/>

	<div>
		<label>Fake input:<input id="input3"/></label>
	</div>
	<br/>

	<div>
		<label>Tree with 'tabbable = "nodes"' (it's a new experimental feature):</label>
		<div id="tree3"></div>
	</div>
	<br/>

	<div>
		<label>Fake input:<input id="input4"/></label>
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
