<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example</title>

	<script src="../lib/jquery.js"></script>

	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
</head>

<body class="example">
	<h1>Fancytree - Examples</h1>
	<p class="description">
		Fancytree is a JavaScript tree view / tree grid plugin with support for keyboard,
		inline editing, filtering, checkboxes, drag'n'drop, and lazy loading.
	</p>
	<p class="description">
		This site presents some live examples for Fancytree.<br>
		<br>
		Select a topic on the navigator to the left and click 'View source code'
		at the bottom of the sample frame for details.<br>
		<br>
		More documentation and infos can be found at the
		<a target="_blank" href="https://github.com/mar10/fancytree/wiki">Documentation Wiki</a>.<br>
		<br>
		Have fun :-)
	</p>

  <!-- http://azu.github.io/github-ribbon-generator/ -->
  <a href="https://github.com/mar10/fancytree/" target="_blank">
    <img style="position: absolute; top: 0; right: 0; border: 0;"
      src="https://github-camo.global.ssl.fastly.net/652c5b9acfaddf3a9c326fa6bde407b87f7be0f4/68747470733a2f2f73332e616d617a6f6e6177732e636f6d2f6769746875622f726962626f6e732f666f726b6d655f72696768745f6f72616e67655f6666373630302e706e67"
      alt="Fork me on GitHub" data-canonical-src="https://s3.amazonaws.com/github/ribbons/forkme_right_orange_ff7600.png">
  </a>

</body>
</html>
