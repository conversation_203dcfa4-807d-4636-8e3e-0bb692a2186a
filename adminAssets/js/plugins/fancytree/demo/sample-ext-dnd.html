<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Drag'n'drop</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.dnd.js"></script>
	<script src="../src/jquery.fancytree.edit.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->
<style type="text/css">
	#draggableSample, #droppableSample {
		height:100px;
		padding:0.5em;
		width:150px;
		border:1px solid #AAAAAA;
	}
	#draggableSample {
		background-color: silver;
		color:#222222;
	}
	#droppableSample {
		background-color: maroon;
		color: white;
	}
	#droppableSample.drophover {
		border: 1px solid green;
	}
</style>
<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		// Attach the fancytree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the fancytree() function:
		$("#tree").fancytree({
			extensions: ["dnd", "edit"],
			// titlesTabbable: true,
			source: {
				url: "ajax-tree-fs.json"
			},
			dnd: {
				autoExpandMS: 400,
				focusOnClick: true,
				preventVoidMoves: true, // Prevent dropping nodes 'before self', etc.
				preventRecursiveMoves: true, // Prevent dropping nodes on own descendants
				dragStart: function(node, data) {
					/** This function MUST be defined to enable dragging for the tree.
					 *  Return false to cancel dragging of node.
					 */
					return true;
				},
				dragEnter: function(node, data) {
					/** data.otherNode may be null for non-fancytree droppables.
					 *  Return false to disallow dropping on node. In this case
					 *  dragOver and dragLeave are not called.
					 *  Return 'over', 'before, or 'after' to force a hitMode.
					 *  Return ['before', 'after'] to restrict available hitModes.
					 *  Any other return value will calc the hitMode from the cursor position.
					 */
					// Prevent dropping a parent below another parent (only sort
					// nodes under the same parent)
/* 					if(node.parent !== data.otherNode.parent){
						return false;
					}
					// Don't allow dropping *over* a node (would create a child)
					return ["before", "after"];
*/
				   return true;
				},
				dragDrop: function(node, data) {
					/** This function MUST be defined to enable dropping of items on
					 *  the tree.
					 */
					data.otherNode.moveTo(node, data.hitMode);
				}
			},
			activate: function(event, data) {
//				alert("activate " + data.node);
			},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			}
		});
	});
</script>
</head>
<body class="example">
	<h1>Example: 'dnd' extension</h1>
	<div class="description">
		<p>
			jQuery UI based Drag-and-Drop support:
		</p>
		<ul>
			<li>Support drag-and-drop of tree nodes (inside the same or between
				different trees).</li>
			<li>Behave like a standard jQuery UI draggable, i.e. allow
				dropping of nodes on standard droppables.</li>
			<li>Behave like a standard jQuery UI droppable, i.e. allow
				dropping of standard draggables.</li>
		</ul>
		<p>
			<b>Note:</b> Consider using the <a href="sample-ext-dnd5.html">
				<i>native</i> drag-and-drop</a> extension instead.<br>
			<b>Status:</b> production.
			<b>Details:</b>
			<a href="https://github.com/mar10/fancytree/wiki/ExtDnd"
				target="_blank" class="external">ext-dnd</a>.
		</p>
	</div>

	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>

	<!-- Add a <table> element where the tree should appear: -->
	<div id="tree">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
