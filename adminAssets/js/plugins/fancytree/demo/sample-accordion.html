<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: Accordion</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">

ul.fancytree-container {
	border: 1px solid gray;
	width: 300px;
	overflow: hidden;
	/* The expand animations result in ugly focus outline artefacts, so hide those. */
	outline: 0;
}
/* Display top level nodes as accordion header. */
ul.fancytree-container >li >.fancytree-node span.fancytree-title {
	box-sizing: border-box;
	width: 100%;
	padding: 4px;
	margin: 0;
	background-color: #ddd;
	border: 1px solid #bbb;
	font-weight: 550;
}
/* Hide  expanders for top-level nodes */
ul.fancytree-container >li >.fancytree-node .fancytree-expander {
	display: none;
}
/* Don't indent level-2 nodes */
ul.fancytree-container >li >ul {
	padding-left: 0;
}

</style>

<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		// Attach the fancytree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the fancytree() function:
		$("#tree").fancytree({
			autoCollapse: true,
			clickFolderMode: 3,
			icon: function(event, data) {
				return !data.node.isTopLevel();
			},
			source: {url: "ajax-tree-products.json"},
			lazyLoad: function(event, data){
				data.result = {url: "ajax-sub2.json"};
			},
			keydown: function(event, data){
				switch( $.ui.fancytree.eventToString(data.originalEvent) ) {
				case "return":
				case "space":
					data.node.toggleExpanded();
					break;
				}
			}
		});
		// For our demo: toggle auto-collapse mode:
		$("input[name=autoCollapse]").on("change", function(e){
			$.ui.fancytree.getTree().options.autoCollapse = $(this).is(":checked");
		});
	});

</script>
</head>
<body class="example">
	<h1>Example: Accordion</h1>
	<div class="description">
		This sample adds some custom CSS to a standard Fancytree, to mimic an accordion
		widget.
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
		<label>
			<input type="checkbox" name="autoCollapse" checked="checked">
			Auto-Collapse
		</label>
	</div>
	<!-- Add a <table> element where the tree should appear: -->
	<div id="tree">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
