<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<title>Fancytree - Example: SVG Icons</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<script defer src="https://use.fontawesome.com/releases/v5.0.13/js/all.js"></script>

	<link href="../src/skin-awesome/ui.fancytree.css" rel="stylesheet" class="skinswitcher">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.glyph.js"></script>

<!-- Start_Exclude: This block is not part of the sample code -->
	<!-- Used for font slider: -->
	<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="../demo/sample.css" rel="stylesheet">
	<script src="../demo/sample.js"></script>

<!-- End_Exclude -->

<style type="text/css">
.fancytree-custom-icon svg {
	width: 1em;
	height: 1em;
	vertical-align: -.125em;
}
</style>


	<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){

		// Convert class name to the ID in the same way that Font Awesome's all.js does it:
		// E.g. "far fa-square" becomes #far-fa-square
		// Then create an <svg><use> tag that references this ID.
		// This svg tag is returned as object { html: TAG }, which will be recognized by
		// the glyph extension and inserted into the Fancytree spans.
		// Note that the actual icons are defined below like
		// <i class="far fa-square" data-fa-symbol></i>.
		// Font Awesome's all.js converts those tags into <svg><symbol> markup with the ID,
		// so they can be referenced instead of having to create them for every node.
		function _svg(className, addClass) {
			var id = className.replace(/ /g, "-"),
				cn = addClass ? " " + addClass : "",
				html = '<svg class="svg-inline--fa fa-w-20' + cn + '"><use xlink:href="#' + id + '"></use></svg>';
			return { html:  html };
		}
		// Initialize Fancytree
		$("#tree").fancytree({
			extensions: ["glyph"],
			checkbox: true,
			selectMode: 3,
			glyph: {
				preset: "",
				map: {
					checkbox: _svg("far fa-square"),
					checkboxSelected: _svg("far fa-check-square"),
					checkboxUnknown: _svg("fas fa-square", "fancytree-helper-indeterminate-cb"),
					// checkboxUnknown: _svg("fas fa-square"),
					radio: _svg("far fa-circle"),
					radioSelected: _svg("fas fa-circle"),
					radioUnknown: _svg("far fa-dot-circle"),
					dragHelper: _svg("fas fa-arrow-right"),
					dropMarker: _svg("fas fa-long-arrow-right"),
					error: _svg("fas fa-exclamation-triangle"),
					expanderClosed: _svg("fas fa-caret-right"),
					expanderLazy: _svg("fas fa-angle-right"),
					expanderOpen: _svg("fas fa-caret-down"),
					loading: _svg("fas fa-spinner", "fa-pulse"),
					nodata: _svg("far fa-meh"),
					noExpander: _svg(""),
					// Default node icons.
					// (Use tree.options.icon callback to define custom icons based on node data)
					doc: _svg("far fa-file"),
					docOpen: _svg("far fa-file"),
					folder: _svg("far fa-folder"),
					folderOpen: _svg("far fa-folder-open")
				}
			},
			icon: function(event, data) {
				// For the sake of this example set specific icons in different ways.
				//
				switch( data.node.title ) {
					case "Art of War":
						// Insert an SVG reference to an SVG symbol (defined below)
						return { html: '<svg><use xlink:href="#svg-android-black"></use></svg>' };
					case "The Hobbit":
						// Insert an <i> tag that will be replaced with an inline SVG graphic
						// by Font Awesome's all.js library.
						// Note: We DON'T want this, since it will be slow for large trees!
						return { html: '<i class="fas fa-book"></i>' };
					case "The Little Prince":
						// Here we use Font Awesome's auto conversion (as above), to create the
						// <i> tags that where created separately below.
						// The nodes nodes contain inline tags that reference those icons:
						return { html: '<svg class="fa-spin"><use xlink:href="#fas-fa-circle-notch"></use></svg>' };
				}
			},
			source: {url: "ajax-tree-products.json", debugDelay: 1000},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json", debugDelay: 3000};
			}
		});
	});
</script>

<!-- Start_Exclude: This block is not part of the sample code -->
<script>
$(function(){
	// Adjust skinswitcher for current folder layout
	function setSize(event, ui){
		$("#tree .fancytree-container").css("font-size", ui.value + "pt");
		$("#curSize").text(ui.value + " pt");
	}
	$( "#fontSlider" ).slider({
		min: 4,
		max: 64,
		value: 10,
		change: setSize,
		slide: setSize
	 }).slider("value", 10);
});
</script>
<!-- End_Exclude -->

</head>

<body class="example">
	<h1>Example: 'glyph' extension with 'font-awesome' theme and Font Awesome 5 SVG/JS  icons</h1>
	<div class="description">
		<ul>
		<li>
			The 'glyph' extension adds <code>fa-...</code> classes to the
			node's <code>span</code> tags, so scalable vector icons as provided by
			<a href="http://fontawesome.io/" class="external" target="_blank">Font Awesome 5+</a>
			can be used.<br>
			See also
			<a href="https://github.com/mar10/fancytree/wiki/ExtGlyph"
				class="external" target="_blank">ext-glyph</a>.
		</ul>
	</div>

	<!--
		Cached SVG icons from Font Awesome 5
		These are converted to <svg><symbol> elements and hidden by Font Awesome's all.js script.
		We reference them in the Fancytree nodes, using <svg><use> elements.
	-->
	<!-- Used by ext-glyph mapping: -->
	<i class="far fa-check-square" data-fa-symbol></i>
	<i class="far fa-circle" data-fa-symbol></i>
	<i class="far fa-dot-circle" data-fa-symbol></i>
	<i class="far fa-file" data-fa-symbol></i>
	<i class="far fa-folder-open" data-fa-symbol></i>
	<i class="far fa-folder" data-fa-symbol></i>
	<i class="far fa-meh" data-fa-symbol></i>
	<i class="far fa-square" data-fa-symbol></i>
	<i class="fas fa-angle-right" data-fa-symbol></i>
	<i class="fas fa-arrow-right" data-fa-symbol></i>
	<i class="fas fa-caret-down" data-fa-symbol></i>
	<i class="fas fa-caret-right" data-fa-symbol></i>
	<i class="fas fa-circle-notch fa-pulse" data-fa-symbol></i>
	<i class="fas fa-circle" data-fa-symbol></i>
	<i class="fas fa-exclamation-triangle" data-fa-symbol></i>
	<i class="fas fa-long-arrow-right" data-fa-symbol></i>
	<i class="fas fa-spinner fa-pulse" data-fa-symbol></i>
	<i class="fas fa-square" data-fa-symbol></i>
	<!-- Used by custom 'icon' demo -->
	<i class="fas fa-dot-circle" data-fa-symbol></i>
	<!-- Native SVG icon, also used by custom 'icon' demo -->
	<svg style="display: none;">
		<symbol id="svg-android-black"
			fill="#000000" height="16" viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 0h24v24H0z" fill="none"/>
			<path d="M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71-.2-.2-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z"/>
		</symbol>
	</svg>

	<hr>
	Font size: <span id="curSize"></span>
	<div id="fontSlider"></div>
	<hr>
	<div id="tree">
	</div>

<!-- Start_Exclude: This block is not part of the sample code -->
	<p id="sampleButtons">
	</p>
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree/">Fancytree project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
<!-- End_Exclude -->
</body>
</html>
