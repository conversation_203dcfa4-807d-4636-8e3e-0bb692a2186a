<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Example: ext-wide</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../src/jquery.fancytree.js"></script>
	<script src="../src/jquery.fancytree.dnd5.js"></script>
	<script src="../src/jquery.fancytree.wide.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../lib/prettify.css" rel="stylesheet">
	<script src="../lib/prettify.js"></script>
	<link href="sample.css" rel="stylesheet">
	<script src="sample.js"></script>
	<!-- End_Exclude -->

<style type="text/css">
.fancytree-container {
	height: 250px;
	overflow: auto;
}
</style>

<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		$("#tree").fancytree({
			extensions: ["dnd5", "wide"],
			autoScroll: true,
			checkbox: true,
			source: {url: "ajax-tree-local.json"},
			// toggleEffect: { effect: "blind", options: {direction: "vertical", scale: "box"}, duration: 200 },
			// toggleEffect: { effect: "drop", options: {direction: "left"}, duration: 400 },
			// toggleEffect: false,
			wide: {
				// iconWidth: "32px",     // Adjust this if @fancy-icon-width != "16px"
				// iconSpacing: "6px", // Adjust this if @fancy-icon-spacing != "3px"
				// labelSpacing: "6px",   // Adjust this if padding between icon and label !=  "3px"
				// levelOfs: "32px"     // Adjust this if ul padding != "16px"
			},
			dnd5: {
				dragStart: function(node, data) { return true; },
				dragEnter: function(node, data) { return true; },
				dragDrop: function(node, data) { data.otherNode.copyTo(node, data.hitMode); }
			},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			}
		});
		$("#tree2").fancytree({
			extensions: [],
			checkbox: true,
			source: {url: "ajax-tree-local.json"},
			lazyLoad: function(event, data) {
				data.result = {url: "ajax-sub2.json"}
			}
		});
	});
</script>
</head>
<body class="example">
	<h1>Example: 'wide' extension</h1>
	<div class="description">
		<p>
			Stretch the selection bar to 100% of the container width
			(<a href="https://github.com/mar10/fancytree/wiki/ExtWide"
				target="_blank" class="external">details...</a>).
		</p>
	</div>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>
	<p id="sampleButtons">
	</p>
	<!-- Add a <table> element where the tree should appear: -->
	<h3>ext-wide: on</h3>
	<div id="tree">
	</div>

	<h3>ext-wide: off</h3>
	<div id="tree2">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
