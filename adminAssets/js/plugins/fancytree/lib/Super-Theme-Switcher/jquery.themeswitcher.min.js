/**
 * jQ<PERSON>y Theme Switcher plugin
 *
 * Copyright (c) 2011 <PERSON> (davehoff.com)
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 */
(function(d){d.fn.themeswitcher=function(c){function f(b){if(null!==a.onselect)a.onselect();l.text(a.buttonpretext+" "+b.title);var h=[],c=b.url;c||(h=a.themepath+a.jqueryuiversion+"/themes/",c=h+b.name+"/jquery-ui.css",h=d('link[href^="'+h+'"]').first());h.length?h[0].href=c:d("<link/>").attr("type","text/css").attr("rel","stylesheet").attr("href",c).appendTo("head");d.cookie(a.cookiename,b.name,{expires:a.cookieexpires,path:a.cookiepath});e.find(".jquery-ui-switcher-title").text(a.buttonpretext+
" "+b.title);a.closeonselect&&g()}function b(a){var b=null;d.each(k,function(d,c){if(c.name.toLowerCase()===a.toLowerCase()||c.title.toLowerCase()===a.toLowerCase())return b=c,!1});return!b?k[0]:b}function g(){if(null!==a.onclose)a.onclose();e.find(".jquery-ui-switcher-list-hldr").slideUp("fast",function(){e.find(".jquery-ui-switcher-link").css({color:"#666",background:"#eee url("+a.imgpath+"buttonbg.png) repeat-x 50% 50%"})})}var e=this,i={},a={loadtheme:"",height:200,width:175,rounded:!0,imgpath:"",
themepath:"https://ajax.googleapis.com/ajax/libs/jqueryui/",jqueryuiversion:"1.8.10",initialtext:"Switch Theme",buttonpretext:"Theme:",closeonselect:!0,buttonheight:14,cookiename:"jquery-ui-theme",themes:[],additionalthemes:[],onopen:null,onclose:null,onselect:null,cookieexpires:365,cookiepath:"/"};c&&(d.each(c,function(a,b){i[a.toLowerCase()]=b}),d.extend(a,i));var k=a.themes.length?a.themes:[{title:"Black Tie",name:"black-tie",icon:"theme_90_black_tie.png"},{title:"Blitzer",name:"blitzer",icon:"theme_90_blitzer.png"},
{title:"Cupertino",name:"cupertino",icon:"theme_90_cupertino.png"},{title:"Dark Hive",name:"dark-hive",icon:"theme_90_dark_hive.png"},{title:"Dot Luv",name:"dot-luv",icon:"theme_90_dot_luv.png"},{title:"Eggplant",name:"eggplant",icon:"theme_90_eggplant.png"},{title:"Excite Bike",name:"excite-bike",icon:"theme_90_excite_bike.png"},{title:"Flick",name:"flick",icon:"theme_90_flick.png"},{title:"Hot Sneaks",name:"hot-sneaks",icon:"theme_90_hot_sneaks.png"},{title:"Humanity",name:"humanity",icon:"theme_90_humanity.png"},
{title:"Le Frog",name:"le-frog",icon:"theme_90_le_frog.png"},{title:"Mint Choc",name:"mint-choc",icon:"theme_90_mint_choco.png"},{title:"Overcast",name:"overcast",icon:"theme_90_overcast.png"},{title:"Pepper Grinder",name:"pepper-grinder",icon:"theme_90_pepper_grinder.png"},{title:"Redmond",name:"redmond",icon:"theme_90_windoze.png"},{title:"Smoothness",name:"smoothness",icon:"theme_90_smoothness.png"},{title:"South Street",name:"south-street",icon:"theme_90_south_street.png"},{title:"Start",name:"start",
icon:"theme_90_start_menu.png"},{title:"Sunny",name:"sunny",icon:"theme_90_sunny.png"},{title:"Swanky Purse",name:"swanky-purse",icon:"theme_90_swanky_purse.png"},{title:"Trontastic",name:"trontastic",icon:"theme_90_trontastic.png"},{title:"UI Darkness",name:"ui-darkness",icon:"theme_90_ui_dark.png"},{title:"UI Lightness",name:"ui-lightness",icon:"theme_90_ui_light.png"},{title:"Vader",name:"vader",icon:"theme_90_black_matte.png"}];a.additionalthemes.length&&d.extend(k,a.additionalthemes);c={cursor:"pointer",
"font-family":"'Trebuchet MS', Verdana, sans-serif","font-size":"11px",color:"#666",background:"#eee url("+a.imgpath+"buttonbg.png) repeat-x 50% 50%",border:"1px solid #CCC","text-decoration":"none",padding:"3px 3px 3px 8px",width:a.width+"px",display:"block",height:a.buttonheight+"px",outline:"0px","line-height":a.buttonheight+"px"};a.rounded&&(c["border-radius"]="6px",c["-moz-border-radius"]="6px",c["-webkit-border-radius"]="6px");var c=d("<a/>").addClass("jquery-ui-switcher-link").css(c).bind({mouseenter:function(){d(this).css({background:"#eee"})},
mouseleave:function(){e.find(".jquery-ui-switcher-list-hldr").is(":visible")||d(this).css({background:"#eee url("+a.imgpath+"buttonbg.png) repeat-x 50% 50%"})},click:function(){if(e.find(".jquery-ui-switcher-list-hldr").is(":visible"))g();else{if(null!==a.onopen)a.onopen();e.find(".jquery-ui-switcher-link").css({color:"#AAA",background:"#000"});e.find(".jquery-ui-switcher-list-hldr").slideDown("fast")}}}),l=d("<span/>").addClass("jquery-ui-switcher-title").appendTo(c);d("<span/>").addClass("jquery-ui-switcher-arrow").css({"float":"right",
width:"16px",height:"16px",background:"url("+a.imgpath+"icon_color_arrow.gif) no-repeat 50% 50%"}).appendTo(c);d.cookie(a.cookiename)?f(b(d.cookie(a.cookiename))):a.loadtheme.length?f(b(a.loadtheme)):l.text(a.initialtext);var j=d("<div/>").addClass("jquery-ui-switcher-list-hldr").css({width:eval(a.width+8)+"px",background:"#000",color:"#FFF","font-family":"'Trebuchet MS', Verdana, sans-serif","font-size":"12px",border:"1px solid #CCC","border-top":"none","z-index":"999999",position:"absolute",top:eval(a.buttonheight+
3)+"px",left:"0px",padding:"3px 3px 3px 0",display:"none"}).bind({mouseleave:function(){g()}});a.rounded&&(j.css("border-radius","0 0 6px 6px"),j.css("-moz-border-radius","0 0 6px 6px"),j.css("-webkit-border-radius","0 0 6px 6px"));var m=d("<ul/>").css({"list-style":"none",margin:"0",padding:"0","overflow-y":"auto","overflow-x":"hidden",height:a.height+"px"}).appendTo(j);d.each(k,function(b,c){var e=d("<li>").css("height","90px").appendTo(m),g=d("<a>").css({display:"block",padding:"5px 3px 5px 5px",
"text-decoration":"none","float":"left",width:"100%",clear:"left"}).bind({mouseenter:function(){g.css("background","url("+a.imgpath+"menuhoverbg.png) repeat-x 50% 50%")},mouseleave:function(){g.css("background","none")},click:function(a){f(d(this).data());a.preventDefault()}}).attr("href","#").data(c).appendTo(e);d("<img>").attr("src",a.imgpath+c.icon).attr("title",c.title).css({"float":"left","margin-right":"5px",border:"1px solid #333"}).appendTo(g);d("<span>").css({"float":"left","padding-top":"5px",
color:"#AAA"}).text(c.title).appendTo(g)});this.css("position","relative");this.append(c);this.append(j);return this}})(jQuery);
(function(d){d.cookie=function(c,f,b){if(1<arguments.length&&(!/Object/.test(Object.prototype.toString.call(f))||null===f||void 0===f)){b=d.extend({},b);if(null===f||void 0===f)b.expires=-1;if("number"===typeof b.expires){var g=b.expires,e=b.expires=new Date;e.setDate(e.getDate()+g)}f=String(f);return document.cookie=[encodeURIComponent(c),"=",b.raw?f:encodeURIComponent(f),b.expires?"; expires="+b.expires.toUTCString():"",b.path?"; path="+b.path:"",b.domain?"; domain="+b.domain:"",b.secure?"; secure":
""].join("")}for(var b=f||{},g=b.raw?function(a){return a}:decodeURIComponent,e=document.cookie.split("; "),i=0,a;a=e[i]&&e[i].split("=");i++)if(g(a[0])===c)return g(a[1]||"");return null}})(jQuery);