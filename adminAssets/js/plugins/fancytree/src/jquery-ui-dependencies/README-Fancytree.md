# Customized jQuery UI libraries for Fancytree

This folder contains a jQuery UI build with all widgets minimally required
by Fancytree:

  - widget.js
  - position.js
  - keycode.js
  - scroll-parent.js
  - unique-id.js
  - <strike>effect.js</strike>
  - <strike>effects/effect-blind.js</strike>

During the build step, the file `jquery-ui.js` is added as part of jquery.fancytree-all-deps.js,
then  minified and a source map generated.


## Files

  - `jquery-ui.js`
    Original jQuery UI library, custom build from https://jqueryui.com/download/.
    Contains (only) all widgets required by Fancytree.
  - `jquery-ui.min.js`
    Original minified version as generated by the download site.
  - `jquery-ui-iife.js`
    Modified version of jquery-ui.js.
    The AMD header is removed an replaced with a plain IIFE wrapper, so this module
    can be included inside the AMD wrapper of jquery.fancytree-all-deps.js.
  - `jquery.fancytree.ui-deps.js`
    Modified version of jquery-ui.js.
    The code is patched, so that the library will not override and replace widgets in case another jQuery UI instance was already included before.
    See [this issue](https://github.com/mar10/fancytree/issues/803#issuecomment-*********) for details.


#### Patching `jquery-ui-iife.js`

`jquery.fancytree-all-deps.min.js` supports AMD and CommonJS module syntax, so
the code is wrapped by a module intro and outro pattern.<br>
The jQuery UI library (as downloaded from https://jqueryui.com/download/) does
the same, but any module can only export one object:<br>
we want `require("jquery.fancytree")` to return the Fancytree widget.

Therefore we modify the original `jquery-ui.js` download an replace the original
UMD headers with a plain closure.
It can then be included in jquery.fancytree-all-deps.min.js, in the same way as the other
Fancytree extensions:

```js
/*
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "jquery" ], factory );
	} else {

		// Browser globals
		factory( jQuery );
	}
}(function( $ ) {
*/
(function( $ ) {

	...

// }));
})(jQuery);
```


#### Patching `jquery.fancytree.ui-deps.js`

This module is distributed as `dist/modules/jquery.fancytree.ui-deps.js` and is `require()`'d by the core module `jquery.fancytree.js`.

The code is patched, so that the library will not override and replace widgets in case another jQuery UI instance was already included before.
(See [this issue](https://github.com/mar10/fancytree/issues/803#issuecomment-*********) for details.)

See [Patch DIFF](https://github.com/mar10/fancytree/commit/4ac9b461e8e633c938610fc01f292355e12720a2#diff-da57115b491a5564bf312f2a8b609b37)


## Updating this Dependencies

1. Download new versions from http://jqueryui.com .
2. Apply modifications as described above.
3. run `grunt build` to test.
