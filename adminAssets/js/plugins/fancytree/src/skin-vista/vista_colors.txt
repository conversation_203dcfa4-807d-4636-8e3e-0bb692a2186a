Dynatree Vista styles

See also: http://www.colorzilla.com/gradient-editor/


both: 
   unselected background: #FCFCFC 'nearly white'
   hover bar (unselected, inactive): #F8FCFE..#EFF9FE (border: #D8F0FA) 'very light blue'
        background: #f8fcfe; /* Old browsers */
        background: -moz-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f8fcfe), color-stop(100%,#eff9fe)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top, #f8fcfe 0%,#eff9fe 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top, #f8fcfe 0%,#eff9fe 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top, #f8fcfe 0%,#eff9fe 100%); /* IE10+ */
        background: linear-gradient(to bottom, #f8fcfe 0%,#eff9fe 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f8fcfe', endColorstr='#eff9fe',GradientType=0 ); /* IE6-9 */   
   active node: #F6FBFD..#D5EFFC (border: #99DEFD)  'light blue'
        background: #f6fbfd; /* Old browsers */
        background: -moz-linear-gradient(top, #f6fbfd 0%, #d5effc 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f6fbfd), color-stop(100%,#d5effc)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top, #f6fbfd 0%,#d5effc 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top, #f6fbfd 0%,#d5effc 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top, #f6fbfd 0%,#d5effc 100%); /* IE10+ */
        background: linear-gradient(to bottom, #f6fbfd 0%,#d5effc 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f6fbfd', endColorstr='#d5effc',GradientType=0 ); /* IE6-9 */
   active node with hover: #F2F9FD..#C4E8FA (border: #B6E6FB) 
        background: #f2f9fd; /* Old browsers */
        background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f2f9fd), color-stop(100%,#c4e8fa)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top, #f2f9fd 0%,#c4e8fa 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top, #f2f9fd 0%,#c4e8fa 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top, #f2f9fd 0%,#c4e8fa 100%); /* IE10+ */
        background: linear-gradient(to bottom, #f2f9fd 0%,#c4e8fa 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f9fd', endColorstr='#c4e8fa',GradientType=0 ); /* IE6-9 */ 

Tree view:
   active node, tree inactive: #FAFAFB..#E5E5E5 (border: #D9D9D9) 'light gray, selected, but tree not active'
        background: #fafafb; /* Old browsers */
        background: -moz-linear-gradient(top, #fafafb 0%, #e5e5e5 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fafafb), color-stop(100%,#e5e5e5)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top, #fafafb 0%,#e5e5e5 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top, #fafafb 0%,#e5e5e5 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top, #fafafb 0%,#e5e5e5 100%); /* IE10+ */
        background: linear-gradient(to bottom, #fafafb 0%,#e5e5e5 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fafafb', endColorstr='#e5e5e5',GradientType=0 ); /* IE6-9 */

List view:
   selected bar: --> active bar
   focus  bar: active + border 1px dotted #090402 (inside the blue border)
   
   table left/right border: #EDEDED 'light gray'
