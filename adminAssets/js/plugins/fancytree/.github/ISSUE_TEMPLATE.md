Thanks for contributing :-)

Please also read the hints:
  https://github.com/mar10/fancytree/wiki/HowtoContribute#report-issues
then remove all unneeded lines from this issue report.

If you are going to ask a question, please use Stackoverflow instead:

    http://stackoverflow.com/questions/tagged/fancytree


### Expected and Actual Behavior

... (Maybe even a screenshot? Any hints on the browser's debug console?)


### Steps to Reproduce the Problem

  1. ...
  2. ...

Could you set up a jsFiddle (http://jsfiddle.net/mar10/KcxRd/),
CodePen (https://codepen.io/mar10/pen/WMWrbq), or
Plunker (http://plnkr.co/edit/8sdy3r?p=preview) ?


### Environment

  - Browser type and version:
  - jQuery and jQuery UI versions:
  - Fancytree version:
    enabled/affected extensions:
