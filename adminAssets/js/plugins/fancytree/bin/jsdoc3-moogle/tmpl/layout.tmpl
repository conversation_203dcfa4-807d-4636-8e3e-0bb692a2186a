<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: <?js= title ?></title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body>

<div id="main">

    <h1 class="page-title"><?js= title ?></h1>

    <?js= content ?>
</div>

<nav>
    <?js= this.nav ?>
</nav>

<br class="clear">

<footer>
  <!-- Start MOOGLE Changes -->

  <!-- Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc <?js= env.version.number ?></a><?js if(env.conf.templates && env.conf.templates.default && env.conf.templates.default.includeDate !== false) { ?> on <?js= (new Date()) ?><?js } ?> -->

  <?js if (env.conf.templates.moogle.footer){ ?>
  	<?js= env.conf.templates.moogle.footer ?>
  <?js } ?>
  <?js if (env.conf.templates.moogle.copyright){ ?>
  	<span class="copyright">
  	<?js= env.conf.templates.moogle.copyright ?>
  	</span>
  <?js } ?>
  <span class="jsdoc-message">
  	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc <?js= env.version.number ?></a>
  	<?js if (env.conf.templates.default.includeDate) { ?>
  		on <?js= (new Date()).toDateString() ?>
  	<?js } ?>
    using a <a href="https://github.com/mar10/fancytree/tree/master/bin/jsdoc3-moogle "
      target="_blank">fancytree custom template</a>.
  </span>
  <!-- End MOOGLE Changes -->
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>

<!-- Start MOOGLE Changes -->

<!-- See https://codepo8.github.io/css-fork-on-github-ribbon/ -->
<?js if(env.conf.templates.moogle.ghForkMe) { ?>
  <style>#forkongithub a{background:#000;color:#fff;text-decoration:none;font-family:arial,sans-serif;text-align:center;font-weight:bold;padding:5px 40px;font-size:1rem;line-height:2rem;position:relative;transition:0.5s;}#forkongithub a:hover{background:#c11;color:#fff;}#forkongithub a::before,#forkongithub a::after{content:"";width:100%;display:block;position:absolute;top:1px;left:0;height:1px;background:#fff;}#forkongithub a::after{bottom:1px;top:auto;}@media screen and (min-width:800px){#forkongithub{position:fixed;display:block;top:0;right:0;width:200px;overflow:hidden;height:200px;z-index:9999;}#forkongithub a{width:200px;position:absolute;top:60px;right:-60px;transform:rotate(45deg);-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);-moz-transform:rotate(45deg);-o-transform:rotate(45deg);box-shadow:4px 4px 10px rgba(0,0,0,0.8);}}
  </style>
  <span id="forkongithub"><a href="<?js= env.conf.templates.moogle.ghForkMe.url ?>">Fork me on GitHub</a></span>
  <?js } ?>

<!-- Google Analytics -->
<?js if (env.conf.templates.moogle.analytics) { ?>
<script>
	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
	(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
	m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,'script','//www.google-analytics.com/analytics.js','ga');

	ga('create', '<?js= env.conf.templates.moogle.analytics.ua ?>', '<?js= env.conf.templates.moogle.analytics.domain ?>');
	ga('send', 'pageview');
</script>
<?js } ?>

<!-- End MOOGLE Changes -->

</body>
</html>
