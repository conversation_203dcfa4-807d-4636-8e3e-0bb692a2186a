<!-- Start MOOGLE Changes -->
<?js
    var self = this;
    var data = obj;
    var methods = data;
?>

<table class="methods">
    <thead>
	<tr>
		<th>Return Type</th>
		<th>Name and Arguments</th>
		<th class="last">Details</th>
	</tr>
	</thead>

	<tbody>
	<?js
        var self = this;
	    methods.forEach(function(m) {
	        if (!m) { return; }
            var params = m.params,
                funcname = m.name,
                returns = m.returns,
                args = "()",
                ret = (m.kind === "class" ? "new" : "void");

            if(returns){
                var returnTypes = [];
                returns.forEach(function(r) {
                    if (r.type && r.type.names) {
                        if (! returnTypes.length) { returnTypes = r.type.names; }
                    }
                });
                ret = (returnTypes.length ? returnTypes.join(' | ') : '');
            }
            if(params){
                args = [];
                params.forEach(function(param) {
                    if (!param) { return; }
                    var name = param.name;
                    if (param.optional || param.nullable) {
                        name = "<span class='optional'>" + name + "</span>";
                    }
                    if (typeof param.defaultvalue !== 'undefined') {
                        name = name + "=<span class='default'>" + param.defaultvalue + "</span>";
                    }
                    args.push(name);
                });
                args = "(" + args.join(", ") + ")";
            }

	?>

        <tr onclick="window.open('#<?js= m.id ?>', '_self')">
            <td class="type">
                <?js= ret ?>
            </td>
            <td class="description">
                <span class="name"><code><?js= m.name + args ?></code></span>
                <?js= (m.description ? ("<div class=''>" + m.description + "</div>") : "") ?>
            </td>
            <td class="last">
                <a class="showHover" href="#<?js= m.id ?>">Details</a>
            </td>
<!--
            <td class="name">
                <code><?js= m.name + args ?></code>
            </td>

            <td class="description last">
                <?js= m.description ?>
            </td>
 -->
        </tr>

	<?js }); ?>
	</tbody>
</table>
<!-- End MOOGLE Changes -->
