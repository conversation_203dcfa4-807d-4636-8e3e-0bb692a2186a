<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Fancytree - Testing doctype NONE</title>

	<script src="//code.jquery.com/jquery-1.12.4.js"></script>
	<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>

	<link href="../../src/skin-win7/ui.fancytree.css" rel="stylesheet">
	<script src="../../src/jquery.fancytree.js"></script>
	<script src="../../src/jquery.fancytree.dnd.js"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="../../lib/prettify.css" rel="stylesheet">
	<script src="../../lib/prettify.js"></script>
	<link href="../../demo/sample.css" rel="stylesheet">
	<script src="../../demo/sample.js"></script>
	<!-- End_Exclude -->
<style type="text/css">
	#draggableSample, #droppableSample {
		height:100px;
		padding:0.5em;
		width:150px;
		border:1px solid #AAAAAA;
	}
	#draggableSample {
		background-color: silver;
		color:#222222;
	}
	#droppableSample {
		background-color: maroon;
		color: white;
	}
	#droppableSample.drophover {
		border: 1px solid green;
	}
</style>
<!-- Add code to initialize the tree when the document is loaded: -->
<script type="text/javascript">
	$(function(){
		// Tell skinswitcher about theme location
		$("select#skinswitcher").skinswitcher("option", "base", "../../src/");
		// Attach the fancytree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the fancytree() function:
		$("#tree").fancytree({
			extensions: ["dnd"],
			source: {
				url: "../unit/ajax-tree-plain.json"
			},
			dnd: {
				preventVoidMoves: true,
				preventRecursiveMoves: true,
				autoExpandMS: 400,
				dragStart: function(node, data) {
					return true;
				},
				dragEnter: function(node, data) {
				   return true;
				},
				dragDrop: function(node, data) {
					data.otherNode.moveTo(node, data.hitMode);
				}
			},
			activate: function(event, data) {
//              alert("activate " + data.node);
			},
			lazyLoad: function(event, data) {
				data.result = {url: "../unit/ajax-sub2.json"}
			}
		});
	});
</script>
</head>
<body class="example">
	<h1>Example: testing DOCTYPE</h1>
	<div>
		<label for="skinswitcher">Skin:</label> <select id="skinswitcher"></select>
	</div>

	<!-- Add a <table> element where the tree should appear: -->
	<div id="tree">
	</div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="https://github.com/mar10/fancytree">jquery.fancytree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="index.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
