<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
	<title>Fancytree test memory leaks</title>

	<script src="//code.jquery.com/jquery-3.4.1.min.js"></script>
	<script src="//code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

	<link href="../../lib/qunit.css" rel="stylesheet" media="screen" />
	<script src="../../lib/qunit.js"></script>

	<link href="../../src/skin-win8/ui.fancytree.css" rel="stylesheet">
	<script src="../../src/jquery.fancytree.js"></script>

	<script src="test-tools.js"></script>
	<script src="test-leaks.js"></script>

<style type="text/css">
</style>

</head>
<body>

 <h1>NOTE: this is a long-running test! Please open a taskmanager before,
  and check memory consumption while executing this test.</h1>

  <div id="qunit"></div>
  <div id="qunit-fixture">test markup, will be hidden</div>
  <hr />
  <div id="tree"></div>

</body>
</html>
