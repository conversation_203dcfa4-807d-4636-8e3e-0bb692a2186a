/**
 * مساعد الطباعة الحرارية المتقدم
 * Advanced Thermal Printing Helper
 */

class ThermalPrintHelper {
    constructor() {
        this.isReady = false;
        this.printSettings = {
            paperWidth: '80mm',
            margin: '0mm',
            fontSize: '11px',
            fontFamily: 'Arial, Tahoma, sans-serif',
            contrast: 1.5,
            brightness: 0.8
        };
        this.init();
    }

    /**
     * تهيئة مساعد الطباعة الحرارية
     */
    init() {
        // تطبيق إعدادات الطباعة الحرارية
        this.applyThermalStyles();
        
        // إضافة مستمعي الأحداث
        this.addEventListeners();
        
        // تحسين جودة الصور
        this.enhanceImageQuality();
        
        this.isReady = true;
        console.log('Thermal Print Helper initialized successfully');
    }

    /**
     * تطبيق أنماط الطباعة الحرارية
     */
    applyThermalStyles() {
        // إنشاء ورقة أنماط ديناميكية للطباعة الحرارية
        const style = document.createElement('style');
        style.id = 'thermal-print-dynamic-styles';
        style.textContent = `
            @media print {
                @page {
                    size: ${this.printSettings.paperWidth} auto;
                    margin: ${this.printSettings.margin};
                }
                
                body {
                    font-family: ${this.printSettings.fontFamily} !important;
                    font-size: ${this.printSettings.fontSize} !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }
                
                .thermal-enhanced {
                    filter: contrast(${this.printSettings.contrast}) brightness(${this.printSettings.brightness}) !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * إضافة مستمعي الأحداث
     */
    addEventListeners() {
        // مستمع لأحداث الطباعة
        window.addEventListener('beforeprint', () => {
            this.beforePrint();
        });

        window.addEventListener('afterprint', () => {
            this.afterPrint();
        });

        // مستمع لتحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.onPageLoad();
        });
    }

    /**
     * تحسين جودة الصور للطباعة الحرارية
     */
    enhanceImageQuality() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // تطبيق تحسينات على الصور
            img.style.imageRendering = 'pixelated';
            img.style.imageRendering = '-moz-crisp-edges';
            img.style.imageRendering = 'crisp-edges';
            
            // إضافة فئة التحسين الحراري
            img.classList.add('thermal-enhanced');
            
            // تحسين الباركود خصوصاً
            if (img.alt && img.alt.toLowerCase().includes('barcode')) {
                this.enhanceBarcodeImage(img);
            }
        });
    }

    /**
     * تحسين صور الباركود
     */
    enhanceBarcodeImage(img) {
        img.style.filter = `contrast(2) brightness(0.7)`;
        img.style.imageRendering = 'pixelated';
        img.classList.add('thermal-barcode-enhanced');
        
        // التأكد من أن الصورة محملة بالكامل
        if (img.complete) {
            this.processBarcodeImage(img);
        } else {
            img.onload = () => this.processBarcodeImage(img);
        }
    }

    /**
     * معالجة صورة الباركود
     */
    processBarcodeImage(img) {
        try {
            // إنشاء canvas لتحسين الصورة
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            
            // رسم الصورة مع تحسينات
            ctx.imageSmoothingEnabled = false;
            ctx.drawImage(img, 0, 0);
            
            // تطبيق تحسينات إضافية
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            this.enhanceImageData(imageData);
            ctx.putImageData(imageData, 0, 0);
            
            // استبدال الصورة الأصلية
            img.src = canvas.toDataURL('image/png');
        } catch (error) {
            console.warn('Could not enhance barcode image:', error);
        }
    }

    /**
     * تحسين بيانات الصورة
     */
    enhanceImageData(imageData) {
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            // تحويل إلى رمادي
            const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
            
            // تطبيق عتبة للحصول على أبيض وأسود نقي
            const threshold = gray > 128 ? 255 : 0;
            
            data[i] = threshold;     // أحمر
            data[i + 1] = threshold; // أخضر
            data[i + 2] = threshold; // أزرق
            // data[i + 3] يبقى كما هو (الشفافية)
        }
    }

    /**
     * قبل الطباعة
     */
    beforePrint() {
        console.log('Preparing for thermal printing...');
        
        // إخفاء العناصر غير المطلوبة
        this.hideNonPrintElements();
        
        // تطبيق تحسينات إضافية
        this.applyPrintEnhancements();
        
        // تحسين النصوص
        this.enhanceTextForPrinting();
    }

    /**
     * بعد الطباعة
     */
    afterPrint() {
        console.log('Thermal printing completed');
        
        // استعادة العناصر المخفية
        this.restoreNonPrintElements();
        
        // إزالة التحسينات المؤقتة
        this.removePrintEnhancements();
    }

    /**
     * عند تحميل الصفحة
     */
    onPageLoad() {
        // تطبيق فئات CSS للطباعة الحرارية
        document.body.classList.add('thermal-print-ready');
        
        // تحسين العناصر الموجودة
        this.enhanceExistingElements();
    }

    /**
     * إخفاء العناصر غير المطلوبة في الطباعة
     */
    hideNonPrintElements() {
        const elementsToHide = document.querySelectorAll('.no-print, .no-thermal-print, button, .btn');
        elementsToHide.forEach(element => {
            element.style.display = 'none';
        });
    }

    /**
     * استعادة العناصر المخفية
     */
    restoreNonPrintElements() {
        const elementsToShow = document.querySelectorAll('.no-print, .no-thermal-print, button, .btn');
        elementsToShow.forEach(element => {
            element.style.display = '';
        });
    }

    /**
     * تطبيق تحسينات الطباعة
     */
    applyPrintEnhancements() {
        // تحسين الحدود
        const borders = document.querySelectorAll('[style*="border"]');
        borders.forEach(element => {
            element.style.borderColor = '#000';
            element.style.borderWidth = '1px';
        });

        // تحسين الخلفيات
        const backgrounds = document.querySelectorAll('[style*="background"]');
        backgrounds.forEach(element => {
            if (element.style.backgroundColor && element.style.backgroundColor !== 'transparent') {
                element.style.backgroundColor = '#f8f8f8';
            }
        });
    }

    /**
     * إزالة تحسينات الطباعة
     */
    removePrintEnhancements() {
        // يمكن إضافة كود لإزالة التحسينات المؤقتة إذا لزم الأمر
    }

    /**
     * تحسين النصوص للطباعة
     */
    enhanceTextForPrinting() {
        const textElements = document.querySelectorAll('p, span, div, td, th, h1, h2, h3, h4, h5, h6');
        textElements.forEach(element => {
            if (element.textContent.trim()) {
                element.style.color = '#000';
                element.style.fontWeight = 'bold';
                element.style.textShadow = 'none';
            }
        });
    }

    /**
     * تحسين العناصر الموجودة
     */
    enhanceExistingElements() {
        // تحسين الجداول
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            table.classList.add('thermal-table');
        });

        // تحسين الباركود
        const barcodes = document.querySelectorAll('img[alt*="barcode"], img[alt*="Barcode"]');
        barcodes.forEach(barcode => {
            barcode.classList.add('thermal-barcode');
        });

        // تحسين النصوص العربية
        const arabicTexts = document.querySelectorAll('[dir="rtl"], .arabic-text');
        arabicTexts.forEach(text => {
            text.classList.add('thermal-arabic');
        });
    }

    /**
     * طباعة محسنة
     */
    enhancedPrint() {
        if (!this.isReady) {
            console.warn('Thermal Print Helper not ready yet');
            return;
        }

        // تطبيق تحسينات إضافية قبل الطباعة
        this.beforePrint();

        // تنفيذ الطباعة
        setTimeout(() => {
            window.print();
        }, 100);
    }

    /**
     * تحديث إعدادات الطباعة
     */
    updateSettings(newSettings) {
        this.printSettings = { ...this.printSettings, ...newSettings };
        this.applyThermalStyles();
    }

    /**
     * فحص دعم الطباعة الحرارية
     */
    checkThermalSupport() {
        const features = {
            printColorAdjust: CSS.supports('print-color-adjust', 'exact'),
            webkitPrintColorAdjust: CSS.supports('-webkit-print-color-adjust', 'exact'),
            imageRendering: CSS.supports('image-rendering', 'pixelated'),
            pageSize: CSS.supports('@page', 'size: 80mm auto')
        };

        console.log('Thermal printing support:', features);
        return features;
    }
}

// تهيئة مساعد الطباعة الحرارية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.thermalPrintHelper = new ThermalPrintHelper();
});

// تصدير الفئة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThermalPrintHelper;
}
