/**
 * تحسينات المحاذاة المركزية للطباعة الحرارية
 * مطابق للصورة المرجعية مع توسيط مثالي
 */

class ThermalCenterAlignment {
    constructor() {
        this.paperWidth = 80; // mm
        this.contentWidth = 70; // mm
        this.margins = (this.paperWidth - this.contentWidth) / 2; // 5mm من كل جانب
        
        this.init();
    }

    init() {
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.applyAlignment());
        } else {
            this.applyAlignment();
        }
    }

    /**
     * تطبيق المحاذاة المركزية على جميع العناصر
     */
    applyAlignment() {
        this.centerPolicyContainers();
        this.centerBarcodeElements();
        this.centerCompanyInfo();
        this.centerPriceSection();
        this.centerSeparators();
        this.optimizeForThermalPrint();
    }

    /**
     * توسيط حاويات البوالص
     */
    centerPolicyContainers() {
        const containers = document.querySelectorAll('.policy-container');
        containers.forEach(container => {
            container.style.width = '76mm';
            container.style.maxWidth = '76mm';
            container.style.margin = '0 auto 3mm auto';
            container.style.padding = '2mm';
            container.style.textAlign = 'center';
            container.style.display = 'block';
            container.style.boxSizing = 'border-box';
        });
    }

    /**
     * توسيط عناصر الباركود
     */
    centerBarcodeElements() {
        // توسيط قسم الباركود
        const barcodeSections = document.querySelectorAll('.barcode-section');
        barcodeSections.forEach(section => {
            section.style.width = '70mm';
            section.style.maxWidth = '70mm';
            section.style.margin = '1mm auto';
            section.style.textAlign = 'center';
        });

        // توسيط صورة الباركود
        const barcodeImages = document.querySelectorAll('.barcode-image');
        barcodeImages.forEach(image => {
            image.style.width = '68mm';
            image.style.maxWidth = '68mm';
            image.style.height = '15mm';
            image.style.margin = '0.5mm auto';
            image.style.display = 'flex';
            image.style.alignItems = 'center';
            image.style.justifyContent = 'center';
        });

        // تحسين جودة صور الباركود
        const barcodeImgs = document.querySelectorAll('.barcode-image img');
        barcodeImgs.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.maxHeight = '100%';
            img.style.objectFit = 'contain';
            img.style.filter = 'contrast(1.5) brightness(0.8)';
            img.style.imageRendering = 'pixelated';
        });

        // توسيط نص الباركود
        const barcodeNumbers = document.querySelectorAll('.barcode-number');
        barcodeNumbers.forEach(number => {
            number.style.textAlign = 'center';
            number.style.fontSize = '12px';
            number.style.fontWeight = 'bold';
            number.style.letterSpacing = '1px';
            number.style.marginTop = '1mm';
        });
    }

    /**
     * توسيط معلومات الشركة
     */
    centerCompanyInfo() {
        const companyInfos = document.querySelectorAll('.company-info');
        companyInfos.forEach(info => {
            info.style.width = '70mm';
            info.style.maxWidth = '70mm';
            info.style.margin = '1.5mm auto';
            info.style.padding = '1mm';
            info.style.textAlign = 'center';
        });

        // توسيط اسم الشركة
        const companyNames = document.querySelectorAll('.company-name');
        companyNames.forEach(name => {
            name.style.textAlign = 'center';
            name.style.fontSize = '14px';
            name.style.fontWeight = 'bold';
            name.style.marginBottom = '1mm';
        });

        // توسيط رقم الهاتف
        const phoneNumbers = document.querySelectorAll('.phone-number');
        phoneNumbers.forEach(phone => {
            phone.style.textAlign = 'center';
            phone.style.fontSize = '11px';
            phone.style.marginBottom = '0.5mm';
        });

        // توسيط العنوان
        const addressLines = document.querySelectorAll('.address-line');
        addressLines.forEach(address => {
            address.style.textAlign = 'center';
            address.style.fontSize = '10px';
            address.style.marginBottom = '1mm';
            address.style.wordWrap = 'break-word';
        });
    }

    /**
     * توسيط قسم السعر
     */
    centerPriceSection() {
        const priceSections = document.querySelectorAll('.price-section');
        priceSections.forEach(section => {
            section.style.width = '70mm';
            section.style.maxWidth = '70mm';
            section.style.margin = '1.5mm auto';
            section.style.padding = '2mm';
            section.style.textAlign = 'center';
            section.style.background = '#f8f8f8';
            section.style.border = '1px solid #000';
        });

        // توسيط قيمة السعر
        const priceValues = document.querySelectorAll('.price-value');
        priceValues.forEach(value => {
            value.style.textAlign = 'center';
            value.style.fontSize = '16px';
            value.style.fontWeight = 'bold';
            value.style.marginBottom = '1mm';
        });
    }

    /**
     * توسيط الفواصل
     */
    centerSeparators() {
        const separators = document.querySelectorAll('.policy-separator');
        separators.forEach(separator => {
            separator.style.width = '70mm';
            separator.style.maxWidth = '70mm';
            separator.style.height = '2mm';
            separator.style.borderBottom = '1px dashed #000';
            separator.style.margin = '2mm auto';
        });
    }

    /**
     * تحسينات خاصة للطباعة الحرارية
     */
    optimizeForThermalPrint() {
        // تطبيق فئات CSS للطباعة الحرارية
        document.body.classList.add('thermal-print-optimized');

        // تحسين الخطوط للطباعة الحرارية
        const allText = document.querySelectorAll('*');
        allText.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.fontSize) {
                element.style.fontFamily = "'Arial', 'Tahoma', sans-serif";
                element.style.lineHeight = '1.3';
            }
        });

        // تحسين الألوان للطباعة الحرارية
        this.optimizeColors();
    }

    /**
     * تحسين الألوان للطباعة الحرارية
     */
    optimizeColors() {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            
            // تحويل الألوان الرمادية إلى أسود أو أبيض
            if (computedStyle.color && computedStyle.color !== 'rgb(0, 0, 0)') {
                element.style.color = '#000';
            }
            
            // تحسين خلفيات العناصر
            if (computedStyle.backgroundColor && 
                computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
                computedStyle.backgroundColor !== 'transparent') {
                if (computedStyle.backgroundColor.includes('248, 248, 248')) {
                    element.style.backgroundColor = '#f8f8f8';
                } else {
                    element.style.backgroundColor = '#fff';
                }
            }
        });
    }

    /**
     * تطبيق المحاذاة عند الطباعة
     */
    applyPrintAlignment() {
        // إضافة CSS خاص بالطباعة
        const printStyle = document.createElement('style');
        printStyle.textContent = `
            @media print {
                body {
                    width: 80mm !important;
                    margin: 0 auto !important;
                    padding: 1mm !important;
                    text-align: center !important;
                }
                
                .policy-container {
                    width: 76mm !important;
                    max-width: 76mm !important;
                    margin: 0 auto 3mm auto !important;
                    text-align: center !important;
                }
                
                .barcode-section,
                .company-info,
                .price-section {
                    width: 70mm !important;
                    max-width: 70mm !important;
                    margin-left: auto !important;
                    margin-right: auto !important;
                    text-align: center !important;
                }
                
                .barcode-image {
                    width: 68mm !important;
                    max-width: 68mm !important;
                    margin: 0.5mm auto !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
            }
        `;
        document.head.appendChild(printStyle);
    }

    /**
     * قياس وتعديل المحاذاة بدقة
     */
    measureAndAdjust() {
        const containers = document.querySelectorAll('.policy-container');
        containers.forEach(container => {
            const rect = container.getBoundingClientRect();
            const parentRect = container.parentElement.getBoundingClientRect();
            
            // حساب المسافة من الجانبين
            const leftMargin = rect.left - parentRect.left;
            const rightMargin = parentRect.right - rect.right;
            
            // تعديل المحاذاة إذا لم تكن متساوية
            if (Math.abs(leftMargin - rightMargin) > 1) {
                const adjustment = (leftMargin - rightMargin) / 2;
                container.style.marginLeft = `${parseFloat(container.style.marginLeft || 0) - adjustment}px`;
            }
        });
    }
}

// تهيئة المحاذاة المركزية عند تحميل الصفحة
window.thermalCenterAlignment = new ThermalCenterAlignment();

// تطبيق المحاذاة عند الطباعة
window.addEventListener('beforeprint', () => {
    window.thermalCenterAlignment.applyPrintAlignment();
    window.thermalCenterAlignment.measureAndAdjust();
});

// إعادة تطبيق المحاذاة عند تغيير حجم النافذة
window.addEventListener('resize', () => {
    setTimeout(() => {
        window.thermalCenterAlignment.applyAlignment();
    }, 100);
});

// تصدير الكلاس للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThermalCenterAlignment;
}
